'use client';

import { useState } from 'react';
import Link from 'next/link';

export default function FAQ() {
  const [searchTerm, setSearchTerm] = useState('');
  const [activeCategory, setActiveCategory] = useState('all');
  const [openItems, setOpenItems] = useState<number[]>([]);

  const categories = [
    { id: 'all', name: 'All Questions', count: 20 },
    { id: 'orders', name: 'Orders & Services', count: 8 },
    { id: 'payments', name: 'Payments & Billing', count: 5 },
    { id: 'account', name: 'Account & Security', count: 4 },
    { id: 'technical', name: 'Technical Support', count: 3 }
  ];

  const faqItems = [
    {
      id: 1,
      category: 'orders',
      question: 'How long does it take to complete an order?',
      answer: 'Most orders start within 1-6 hours and complete within 24-72 hours depending on the service and quantity. Instant services like likes and views typically complete within minutes, while followers and subscribers may take longer for natural delivery.'
    },
    {
      id: 2,
      category: 'orders',
      question: 'Are your services safe for my account?',
      answer: 'Yes, all our services are 100% safe and comply with platform guidelines. We use gradual delivery methods, real accounts, and follow platform best practices to ensure your account remains secure. We have a 99.9% safety record.'
    },
    {
      id: 3,
      category: 'payments',
      question: 'What payment methods do you accept?',
      answer: 'We accept multiple payment methods including PayPal, Vodafone Cash, InstaPay, and direct bank transfers. All payments are processed securely and your financial information is protected.'
    },
    {
      id: 4,
      category: 'orders',
      question: 'Do you offer refunds?',
      answer: 'Yes, we offer refunds for orders that cannot be completed due to technical issues or service unavailability. Refill guarantees are also provided for most services for 30 days after completion.'
    },
    {
      id: 5,
      category: 'orders',
      question: 'Can I cancel my order?',
      answer: 'Orders can be cancelled if they haven\'t started processing yet. Once processing begins, cancellation may not be possible, but we can discuss partial refunds based on the completion status.'
    },
    {
      id: 6,
      category: 'payments',
      question: 'How do I add funds to my account?',
      answer: 'You can add funds through the Payments section in your dashboard. Choose your preferred payment method, enter the amount, and follow the instructions. Funds are typically added instantly for most payment methods.'
    },
    {
      id: 7,
      category: 'account',
      question: 'How do I create an account?',
      answer: 'Click on "Sign Up" in the top navigation, fill in your details including name, email, and password. Verify your email address and you\'re ready to start using our services.'
    },
    {
      id: 8,
      category: 'orders',
      question: 'What information do I need to provide for an order?',
      answer: 'Typically, you need to provide the link to your profile, post, or video. Some services may require additional information like custom comments or usernames. All required fields are clearly marked during the ordering process.'
    },
    {
      id: 9,
      category: 'technical',
      question: 'Why is my order not starting?',
      answer: 'Orders may be delayed due to high demand, incorrect link format, or account privacy settings. Check that your profile/post is public and the link is correct. Contact support if the issue persists.'
    },
    {
      id: 10,
      category: 'payments',
      question: 'Are there any hidden fees?',
      answer: 'No, we believe in transparent pricing. The price you see is the price you pay. Some payment methods may have processing fees which are clearly displayed before payment.'
    },
    {
      id: 11,
      category: 'account',
      question: 'How do I reset my password?',
      answer: 'Click on "Forgot Password" on the login page, enter your email address, and follow the instructions in the reset email. Make sure to check your spam folder if you don\'t receive the email.'
    },
    {
      id: 12,
      category: 'orders',
      question: 'Can I order for multiple accounts?',
      answer: 'Yes, you can place orders for different social media accounts. Just make sure to use the correct links for each order and that all accounts comply with platform terms of service.'
    },
    {
      id: 13,
      category: 'technical',
      question: 'What browsers do you support?',
      answer: 'Our platform works on all modern browsers including Chrome, Firefox, Safari, and Edge. For the best experience, we recommend using the latest version of your preferred browser.'
    },
    {
      id: 14,
      category: 'account',
      question: 'How do I update my profile information?',
      answer: 'Go to your Profile section in the dashboard, click on the information you want to update, make your changes, and save. Some changes may require email verification.'
    },
    {
      id: 15,
      category: 'orders',
      question: 'What happens if my order doesn\'t complete?',
      answer: 'If an order doesn\'t complete due to technical issues, we provide either a refund or a replacement order. Our support team will investigate and resolve the issue promptly.'
    },
    {
      id: 16,
      category: 'payments',
      question: 'How long do refunds take?',
      answer: 'Refunds are typically processed within 24-48 hours to your account balance. For payment method refunds, it may take 3-7 business days depending on your payment provider.'
    },
    {
      id: 17,
      category: 'technical',
      question: 'Is there a mobile app?',
      answer: 'Currently, we offer a mobile-optimized website that works perfectly on all devices. A dedicated mobile app is in development and will be available soon.'
    },
    {
      id: 18,
      category: 'account',
      question: 'How do I enable two-factor authentication?',
      answer: 'Go to Profile > Security settings and enable two-factor authentication. You can use an authenticator app or SMS verification for added account security.'
    },
    {
      id: 19,
      category: 'orders',
      question: 'Do you provide customer support?',
      answer: 'Yes, we offer 24/7 customer support through live chat, email, and WhatsApp. Our support team is always ready to help with any questions or issues.'
    },
    {
      id: 20,
      category: 'payments',
      question: 'What is the minimum order amount?',
      answer: 'Minimum order amounts vary by service, typically starting from $1. The minimum amount for each service is clearly displayed on the service page.'
    }
  ];

  const toggleItem = (id: number) => {
    setOpenItems(prev => 
      prev.includes(id) 
        ? prev.filter(item => item !== id)
        : [...prev, id]
    );
  };

  const filteredItems = faqItems.filter(item => {
    const matchesCategory = activeCategory === 'all' || item.category === activeCategory;
    const matchesSearch = item.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.answer.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <Link href="/" className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-sm">T</span>
                </div>
                <span className="text-xl font-bold text-gray-900">Trendz</span>
              </Link>
            </div>
            
            <nav className="hidden md:flex space-x-8">
              <Link href="/dashboard" className="text-gray-600 hover:text-purple-600">Dashboard</Link>
              <Link href="/services" className="text-gray-600 hover:text-purple-600">Services</Link>
              <Link href="/support" className="text-gray-600 hover:text-purple-600">Support</Link>
              <Link href="/faq" className="text-purple-600 font-medium">FAQ</Link>
            </nav>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Page Header */}
        <div className="text-center mb-12">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">Frequently Asked Questions</h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Find answers to common questions about our services, payments, and platform.
          </p>
        </div>

        {/* Search */}
        <div className="max-w-2xl mx-auto mb-8">
          <div className="relative">
            <input
              type="text"
              placeholder="Search FAQ..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-12 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-purple-500 focus:border-purple-500 text-lg"
            />
            <svg className="w-6 h-6 text-gray-400 absolute left-4 top-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Categories Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow p-6 sticky top-8">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Categories</h3>
              <div className="space-y-2">
                {categories.map((category) => (
                  <button
                    key={category.id}
                    onClick={() => setActiveCategory(category.id)}
                    className={`w-full text-left px-3 py-2 rounded-md transition-colors ${
                      activeCategory === category.id
                        ? 'bg-purple-100 text-purple-700'
                        : 'text-gray-600 hover:bg-gray-100'
                    }`}
                  >
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">{category.name}</span>
                      <span className="text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded-full">
                        {category.count}
                      </span>
                    </div>
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* FAQ Items */}
          <div className="lg:col-span-3">
            <div className="space-y-4">
              {filteredItems.length === 0 ? (
                <div className="bg-white rounded-lg shadow p-8 text-center">
                  <div className="text-4xl mb-4">🔍</div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No results found</h3>
                  <p className="text-gray-600">
                    Try adjusting your search terms or browse different categories.
                  </p>
                </div>
              ) : (
                filteredItems.map((item) => (
                  <div key={item.id} className="bg-white rounded-lg shadow">
                    <button
                      onClick={() => toggleItem(item.id)}
                      className="w-full px-6 py-4 text-left focus:outline-none hover:bg-gray-50 transition-colors"
                    >
                      <div className="flex justify-between items-center">
                        <h4 className="text-lg font-medium text-gray-900 pr-4">{item.question}</h4>
                        <svg
                          className={`w-5 h-5 text-gray-500 transform transition-transform ${
                            openItems.includes(item.id) ? 'rotate-180' : ''
                          }`}
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        </svg>
                      </div>
                    </button>
                    {openItems.includes(item.id) && (
                      <div className="px-6 pb-4 border-t border-gray-100">
                        <p className="text-gray-600 leading-relaxed pt-4">{item.answer}</p>
                      </div>
                    )}
                  </div>
                ))
              )}
            </div>

            {/* Contact Support */}
            <div className="mt-12 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg p-8 text-white text-center">
              <h3 className="text-xl font-bold mb-2">Still have questions?</h3>
              <p className="mb-6 opacity-90">
                Our support team is available 24/7 to help you with any questions or concerns.
              </p>
              <Link
                href="/support"
                className="inline-flex items-center bg-white text-purple-600 px-6 py-3 rounded-md font-medium hover:bg-gray-100 transition-colors"
              >
                Contact Support
                <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
