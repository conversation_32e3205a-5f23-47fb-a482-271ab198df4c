export default function Services() {
  const platforms = [
    { name: 'Facebook', icon: '', color: 'bg-blue-500', services: ['Likes', 'Followers', 'Comments', 'Shares'] },
    { name: 'Instagram', icon: '', color: 'bg-pink-500', services: ['Followers', 'Likes', 'Comments', 'Story Views'] },
    { name: 'TikTok', icon: '', color: 'bg-black', services: ['Views', 'Likes', 'Followers', 'Comments'] },
    { name: 'YouTube', icon: '', color: 'bg-red-500', services: ['Views', 'Subscribers', 'Likes', 'Comments'] },
    { name: 'Twitter', icon: '', color: 'bg-blue-400', services: ['Followers', 'Likes', 'Retweets', 'Comments'] },
    { name: 'Snapchat', icon: '', color: 'bg-yellow-400', services: ['Views', 'Followers'] }
  ];

  const featuredServices = [
    {
      platform: 'Instagram',
      service: 'Instagram Followers',
      price: '.50',
      per: '1000',
      quality: 'High Quality',
      speed: 'Fast',
      icon: ''
    },
    {
      platform: 'TikTok',
      service: 'TikTok Views',
      price: '.20',
      per: '1000',
      quality: 'Premium',
      speed: 'Instant',
      icon: ''
    },
    {
      platform: 'YouTube',
      service: 'YouTube Views',
      price: '.00',
      per: '1000',
      quality: 'High Retention',
      speed: 'Fast',
      icon: ''
    },
    {
      platform: 'Facebook',
      service: 'Facebook Likes',
      price: '.80',
      per: '1000',
      quality: 'Real Users',
      speed: 'Medium',
      icon: ''
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-600 to-blue-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <h1 className="text-4xl font-bold mb-4">Our Services</h1>
            <p className="text-xl text-purple-100 max-w-2xl mx-auto">
              Choose from our wide range of social media marketing services to boost your online presence
            </p>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Platform Categories */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">Choose Your Platform</h2>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
            {platforms.map((platform, index) => (
              <div key={index} className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow cursor-pointer group">
                <div className={`w-16 h-16 ${platform.color} rounded-lg flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform`}>
                  <span className="text-3xl">{platform.icon}</span>
                </div>
                <h3 className="font-semibold text-center text-gray-900 mb-2">{platform.name}</h3>
                <div className="text-xs text-gray-600 text-center">
                  {platform.services.map((service, idx) => (
                    <span key={idx} className="inline-block bg-gray-100 rounded-full px-2 py-1 mr-1 mb-1">
                      {service}
                    </span>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Featured Services */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">Featured Services</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {featuredServices.map((service, index) => (
              <div key={index} className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6">
                <div className="flex items-center mb-4">
                  <span className="text-2xl mr-3">{service.icon}</span>
                  <div>
                    <h3 className="font-semibold text-gray-900">{service.service}</h3>
                    <p className="text-sm text-gray-600">{service.platform}</p>
                  </div>
                </div>
                
                <div className="mb-4">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-2xl font-bold text-purple-600">{service.price}</span>
                    <span className="text-sm text-gray-600">per {service.per}</span>
                  </div>
                  
                  <div className="flex justify-between text-sm mb-2">
                    <span className="text-gray-600">Quality:</span>
                    <span className="font-medium text-green-600">{service.quality}</span>
                  </div>
                  
                  <div className="flex justify-between text-sm mb-4">
                    <span className="text-gray-600">Speed:</span>
                    <span className="font-medium text-blue-600">{service.speed}</span>
                  </div>
                </div>
                
                <button className="w-full bg-purple-600 text-white py-2 px-4 rounded-md hover:bg-purple-700 transition-colors font-medium">
                  Order Now
                </button>
              </div>
            ))}
          </div>
        </div>

        {/* Why Choose Us */}
        <div className="bg-white rounded-lg shadow-md p-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">Why Choose Our Services?</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">High Quality</h3>
              <p className="text-gray-600">All our services provide real, high-quality engagement from active users.</p>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Fast Delivery</h3>
              <p className="text-gray-600">Most orders start within minutes and complete within 24-48 hours.</p>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 2.25a9.75 9.75 0 109.75 9.75A9.75 9.75 0 0012 2.25z" />
                </svg>
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">24/7 Support</h3>
              <p className="text-gray-600">Our customer support team is available round the clock to help you.</p>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="mt-12 text-center">
          <div className="bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg p-8 text-white">
            <h2 className="text-2xl font-bold mb-4">Ready to Get Started?</h2>
            <p className="text-purple-100 mb-6">Join thousands of satisfied customers and boost your social media presence today!</p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a href="/auth/register" className="bg-white text-purple-600 px-6 py-3 rounded-md font-semibold hover:bg-gray-100 transition-colors">
                Create Free Account
              </a>
              <a href="/auth/login" className="border-2 border-white text-white px-6 py-3 rounded-md font-semibold hover:bg-white hover:text-purple-600 transition-colors">
                Sign In
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
