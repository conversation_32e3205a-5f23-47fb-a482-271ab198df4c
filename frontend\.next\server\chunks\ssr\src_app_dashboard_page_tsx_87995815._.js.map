{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/trendz/frontend/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\n\nexport default function Dashboard() {\n  const [user] = useState({\n    name: '<PERSON>',\n    email: '<EMAIL>',\n    balance: 125.50,\n    totalSpent: 450.75,\n    totalOrders: 23,\n    activeOrders: 3\n  });\n\n  const recentOrders = [\n    {\n      id: 'TRZ001',\n      service: 'Instagram Followers',\n      quantity: 1000,\n      price: 12.50,\n      status: 'completed',\n      date: '2025-01-30'\n    },\n    {\n      id: 'TRZ002',\n      service: 'TikTok Views',\n      quantity: 5000,\n      price: 8.75,\n      status: 'in_progress',\n      date: '2025-01-29'\n    },\n    {\n      id: 'TRZ003',\n      service: 'YouTube Subscribers',\n      quantity: 500,\n      price: 25.00,\n      status: 'pending',\n      date: '2025-01-28'\n    }\n  ];\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'completed': return 'bg-green-100 text-green-800';\n      case 'in_progress': return 'bg-blue-100 text-blue-800';\n      case 'pending': return 'bg-yellow-100 text-yellow-800';\n      case 'cancelled': return 'bg-red-100 text-red-800';\n      default: return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const getStatusText = (status: string) => {\n    switch (status) {\n      case 'completed': return 'مكتمل';\n      case 'in_progress': return 'قيد التنفيذ';\n      case 'pending': return 'معلق';\n      case 'cancelled': return 'ملغي';\n      default: return status;\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <div className=\"bg-white shadow\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center py-6\">\n            <div className=\"flex items-center\">\n              <Link href=\"/\" className=\"flex items-center space-x-2\">\n                <div className=\"w-8 h-8 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg flex items-center justify-center\">\n                  <span className=\"text-white font-bold text-sm\">T</span>\n                </div>\n                <span className=\"text-xl font-bold text-gray-900\">Trendz</span>\n              </Link>\n            </div>\n            \n            <nav className=\"hidden md:flex space-x-8\">\n              <Link href=\"/dashboard\" className=\"text-purple-600 font-medium\">Dashboard</Link>\n              <Link href=\"/services\" className=\"text-gray-600 hover:text-purple-600\">Services</Link>\n              <Link href=\"/orders\" className=\"text-gray-600 hover:text-purple-600\">Orders</Link>\n              <Link href=\"/payments\" className=\"text-gray-600 hover:text-purple-600\">Payments</Link>\n              <Link href=\"/profile\" className=\"text-gray-600 hover:text-purple-600\">Profile</Link>\n            </nav>\n\n            <div className=\"flex items-center space-x-4\">\n              <div className=\"bg-gray-100 px-3 py-1 rounded-full\">\n                <span className=\"text-sm font-medium\">${user.balance.toFixed(2)}</span>\n              </div>\n              <div className=\"w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center\">\n                <span className=\"text-white text-sm font-medium\">{user.name.charAt(0)}</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Welcome Section */}\n        <div className=\"mb-8\">\n          <h1 className=\"text-2xl font-bold text-gray-900\">Welcome back, {user.name}!</h1>\n          <p className=\"text-gray-600\">Here's what's happening with your account today.</p>\n        </div>\n\n        {/* Stats Cards */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-2 bg-green-100 rounded-lg\">\n                <svg className=\"w-6 h-6 text-green-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\" />\n                </svg>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Current Balance</p>\n                <p className=\"text-2xl font-bold text-gray-900\">${user.balance.toFixed(2)}</p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-2 bg-blue-100 rounded-lg\">\n                <svg className=\"w-6 h-6 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z\" />\n                </svg>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Total Orders</p>\n                <p className=\"text-2xl font-bold text-gray-900\">{user.totalOrders}</p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-2 bg-purple-100 rounded-lg\">\n                <svg className=\"w-6 h-6 text-purple-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6\" />\n                </svg>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Total Spent</p>\n                <p className=\"text-2xl font-bold text-gray-900\">${user.totalSpent.toFixed(2)}</p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-2 bg-orange-100 rounded-lg\">\n                <svg className=\"w-6 h-6 text-orange-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                </svg>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Active Orders</p>\n                <p className=\"text-2xl font-bold text-gray-900\">{user.activeOrders}</p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Quick Actions */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8\">\n          <div className=\"lg:col-span-2\">\n            <div className=\"bg-white rounded-lg shadow\">\n              <div className=\"px-6 py-4 border-b border-gray-200\">\n                <h3 className=\"text-lg font-medium text-gray-900\">Recent Orders</h3>\n              </div>\n              <div className=\"p-6\">\n                <div className=\"space-y-4\">\n                  {recentOrders.map((order) => (\n                    <div key={order.id} className=\"flex items-center justify-between p-4 bg-gray-50 rounded-lg\">\n                      <div className=\"flex-1\">\n                        <div className=\"flex items-center justify-between\">\n                          <h4 className=\"text-sm font-medium text-gray-900\">{order.service}</h4>\n                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(order.status)}`}>\n                            {getStatusText(order.status)}\n                          </span>\n                        </div>\n                        <p className=\"text-sm text-gray-600\">Order #{order.id} • {order.quantity.toLocaleString()} items</p>\n                        <p className=\"text-sm text-gray-500\">{order.date}</p>\n                      </div>\n                      <div className=\"ml-4 text-right\">\n                        <p className=\"text-sm font-medium text-gray-900\">${order.price.toFixed(2)}</p>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n                <div className=\"mt-6\">\n                  <Link href=\"/orders\" className=\"text-purple-600 hover:text-purple-500 text-sm font-medium\">\n                    View all orders →\n                  </Link>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"space-y-6\">\n            <div className=\"bg-white rounded-lg shadow p-6\">\n              <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Quick Actions</h3>\n              <div className=\"space-y-3\">\n                <Link href=\"/services\" className=\"block w-full bg-purple-600 text-white text-center py-2 px-4 rounded-md hover:bg-purple-700 transition-colors\">\n                  Browse Services\n                </Link>\n                <Link href=\"/payments\" className=\"block w-full bg-green-600 text-white text-center py-2 px-4 rounded-md hover:bg-green-700 transition-colors\">\n                  Add Funds\n                </Link>\n                <Link href=\"/orders\" className=\"block w-full border border-gray-300 text-gray-700 text-center py-2 px-4 rounded-md hover:bg-gray-50 transition-colors\">\n                  View Orders\n                </Link>\n              </div>\n            </div>\n\n            <div className=\"bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg p-6 text-white\">\n              <h3 className=\"text-lg font-medium mb-2\">Need Help?</h3>\n              <p className=\"text-purple-100 text-sm mb-4\">\n                Our support team is available 24/7 to assist you with any questions.\n              </p>\n              <Link href=\"/support\" className=\"inline-flex items-center text-sm font-medium text-white hover:text-purple-100\">\n                Contact Support\n                <svg className=\"w-4 h-4 ml-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                </svg>\n              </Link>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,CAAC,KAAK,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACtB,MAAM;QACN,OAAO;QACP,SAAS;QACT,YAAY;QACZ,aAAa;QACb,cAAc;IAChB;IAEA,MAAM,eAAe;QACnB;YACE,IAAI;YACJ,SAAS;YACT,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;QACR;KACD;IAED,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAe,OAAO;YAC3B,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAa,OAAO;YACzB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAe,OAAO;YAC3B,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAa,OAAO;YACzB;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;;sDACvB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;sDAEjD,8OAAC;4CAAK,WAAU;sDAAkC;;;;;;;;;;;;;;;;;0CAItD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAa,WAAU;kDAA8B;;;;;;kDAChE,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAY,WAAU;kDAAsC;;;;;;kDACvE,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAU,WAAU;kDAAsC;;;;;;kDACrE,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAY,WAAU;kDAAsC;;;;;;kDACvE,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAsC;;;;;;;;;;;;0CAGxE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;;gDAAsB;gDAAE,KAAK,OAAO,CAAC,OAAO,CAAC;;;;;;;;;;;;kDAE/D,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAAkC,KAAK,IAAI,CAAC,MAAM,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO7E,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;oCAAmC;oCAAe,KAAK,IAAI;oCAAC;;;;;;;0CAC1E,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;kCAI/B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAAyB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAChF,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;;wDAAmC;wDAAE,KAAK,OAAO,CAAC,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;0CAK7E,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAAwB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAC/E,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;8DAAoC,KAAK,WAAW;;;;;;;;;;;;;;;;;;;;;;;0CAKvE,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAA0B,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACjF,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;;wDAAmC;wDAAE,KAAK,UAAU,CAAC,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;0CAKhF,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAA0B,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACjF,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;8DAAoC,KAAK,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO1E,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAG,WAAU;0DAAoC;;;;;;;;;;;sDAEpD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACZ,aAAa,GAAG,CAAC,CAAC,sBACjB,8OAAC;4DAAmB,WAAU;;8EAC5B,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAG,WAAU;8FAAqC,MAAM,OAAO;;;;;;8FAChE,8OAAC;oFAAK,WAAW,CAAC,2CAA2C,EAAE,eAAe,MAAM,MAAM,GAAG;8FAC1F,cAAc,MAAM,MAAM;;;;;;;;;;;;sFAG/B,8OAAC;4EAAE,WAAU;;gFAAwB;gFAAQ,MAAM,EAAE;gFAAC;gFAAI,MAAM,QAAQ,CAAC,cAAc;gFAAG;;;;;;;sFAC1F,8OAAC;4EAAE,WAAU;sFAAyB,MAAM,IAAI;;;;;;;;;;;;8EAElD,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEAAE,WAAU;;4EAAoC;4EAAE,MAAM,KAAK,CAAC,OAAO,CAAC;;;;;;;;;;;;;2DAZjE,MAAM,EAAE;;;;;;;;;;8DAiBtB,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAU,WAAU;kEAA4D;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQnG,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAyC;;;;;;0DACvD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAY,WAAU;kEAA+G;;;;;;kEAGhJ,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAY,WAAU;kEAA6G;;;;;;kEAG9I,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAU,WAAU;kEAAwH;;;;;;;;;;;;;;;;;;kDAM3J,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA2B;;;;;;0DACzC,8OAAC;gDAAE,WAAU;0DAA+B;;;;;;0DAG5C,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;;oDAAgF;kEAE9G,8OAAC;wDAAI,WAAU;wDAAe,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEACtE,cAAA,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASvF", "debugId": null}}]}