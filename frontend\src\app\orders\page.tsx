'use client';

import { useState } from 'react';
import Link from 'next/link';

export default function Orders() {
  const [activeFilter, setActiveFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');

  const orders = [
    {
      id: 'TRZ001',
      service: 'Instagram Followers',
      platform: 'Instagram',
      quantity: 1000,
      delivered: 1000,
      price: 12.50,
      status: 'completed',
      date: '2025-01-30',
      link: 'https://instagram.com/username',
      startCount: 1250,
      remains: 0,
      speed: 'Fast'
    },
    {
      id: 'TRZ002',
      service: 'TikTok Views',
      platform: 'TikTok',
      quantity: 5000,
      delivered: 3200,
      price: 8.75,
      status: 'in_progress',
      date: '2025-01-29',
      link: 'https://tiktok.com/@username/video/123',
      startCount: 850,
      remains: 1800,
      speed: 'Instant'
    },
    {
      id: 'TRZ003',
      service: 'YouTube Subscribers',
      platform: 'YouTube',
      quantity: 500,
      delivered: 0,
      price: 25.00,
      status: 'pending',
      date: '2025-01-28',
      link: 'https://youtube.com/channel/UC123',
      startCount: 1200,
      remains: 500,
      speed: 'Medium'
    },
    {
      id: 'TRZ004',
      service: 'Facebook Likes',
      platform: 'Facebook',
      quantity: 2000,
      delivered: 1500,
      price: 15.30,
      status: 'partial',
      date: '2025-01-27',
      link: 'https://facebook.com/post/123',
      startCount: 320,
      remains: 500,
      speed: 'Fast'
    },
    {
      id: 'TRZ005',
      service: 'Twitter Followers',
      platform: 'Twitter',
      quantity: 800,
      delivered: 0,
      price: 18.40,
      status: 'cancelled',
      date: '2025-01-26',
      link: 'https://twitter.com/username',
      startCount: 450,
      remains: 800,
      speed: 'Medium'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'in_progress': return 'bg-blue-100 text-blue-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'partial': return 'bg-orange-100 text-orange-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      case 'refunded': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed': return 'مكتمل';
      case 'in_progress': return 'قيد التنفيذ';
      case 'pending': return 'معلق';
      case 'partial': return 'جزئي';
      case 'cancelled': return 'ملغي';
      case 'refunded': return 'مسترد';
      default: return status;
    }
  };

  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case 'Instagram': return '📷';
      case 'TikTok': return '🎵';
      case 'YouTube': return '📺';
      case 'Facebook': return '📘';
      case 'Twitter': return '🐦';
      case 'Snapchat': return '👻';
      default: return '📱';
    }
  };

  const filteredOrders = orders.filter(order => {
    const matchesFilter = activeFilter === 'all' || order.status === activeFilter;
    const matchesSearch = order.service.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         order.id.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesFilter && matchesSearch;
  });

  const getProgress = (delivered: number, quantity: number) => {
    return Math.round((delivered / quantity) * 100);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <Link href="/" className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-sm">T</span>
                </div>
                <span className="text-xl font-bold text-gray-900">Trendz</span>
              </Link>
            </div>
            
            <nav className="hidden md:flex space-x-8">
              <Link href="/dashboard" className="text-gray-600 hover:text-purple-600">Dashboard</Link>
              <Link href="/services" className="text-gray-600 hover:text-purple-600">Services</Link>
              <Link href="/orders" className="text-purple-600 font-medium">Orders</Link>
              <Link href="/payments" className="text-gray-600 hover:text-purple-600">Payments</Link>
              <Link href="/profile" className="text-gray-600 hover:text-purple-600">Profile</Link>
            </nav>

            <div className="flex items-center space-x-4">
              <Link href="/services" className="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 transition-colors">
                New Order
              </Link>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-2xl font-bold text-gray-900">My Orders</h1>
          <p className="text-gray-600">Track and manage all your orders</p>
        </div>

        {/* Filters and Search */}
        <div className="bg-white rounded-lg shadow mb-6">
          <div className="p-6">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
              {/* Status Filters */}
              <div className="flex flex-wrap gap-2">
                {[
                  { key: 'all', label: 'All Orders', count: orders.length },
                  { key: 'pending', label: 'Pending', count: orders.filter(o => o.status === 'pending').length },
                  { key: 'in_progress', label: 'In Progress', count: orders.filter(o => o.status === 'in_progress').length },
                  { key: 'completed', label: 'Completed', count: orders.filter(o => o.status === 'completed').length },
                  { key: 'partial', label: 'Partial', count: orders.filter(o => o.status === 'partial').length }
                ].map((filter) => (
                  <button
                    key={filter.key}
                    onClick={() => setActiveFilter(filter.key)}
                    className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                      activeFilter === filter.key
                        ? 'bg-purple-600 text-white'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    {filter.label} ({filter.count})
                  </button>
                ))}
              </div>

              {/* Search */}
              <div className="relative">
                <input
                  type="text"
                  placeholder="Search orders..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-purple-500 focus:border-purple-500"
                />
                <svg className="w-5 h-5 text-gray-400 absolute left-3 top-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
            </div>
          </div>
        </div>

        {/* Orders List */}
        <div className="space-y-4">
          {filteredOrders.length === 0 ? (
            <div className="bg-white rounded-lg shadow p-8 text-center">
              <svg className="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
              </svg>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No orders found</h3>
              <p className="text-gray-600 mb-4">You haven't placed any orders yet or no orders match your search.</p>
              <Link href="/services" className="bg-purple-600 text-white px-6 py-2 rounded-md hover:bg-purple-700 transition-colors">
                Browse Services
              </Link>
            </div>
          ) : (
            filteredOrders.map((order) => (
              <div key={order.id} className="bg-white rounded-lg shadow hover:shadow-md transition-shadow">
                <div className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-4">
                      <div className="text-3xl">{getPlatformIcon(order.platform)}</div>
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <h3 className="text-lg font-medium text-gray-900">{order.service}</h3>
                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(order.status)}`}>
                            {getStatusText(order.status)}
                          </span>
                        </div>
                        <p className="text-sm text-gray-600 mb-1">Order #{order.id}</p>
                        <p className="text-sm text-gray-500">Placed on {order.date}</p>
                        <p className="text-sm text-gray-500 truncate">Link: {order.link}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-lg font-bold text-gray-900">${order.price.toFixed(2)}</p>
                      <p className="text-sm text-gray-600">{order.quantity.toLocaleString()} items</p>
                    </div>
                  </div>

                  {/* Progress Bar */}
                  {order.status === 'in_progress' || order.status === 'partial' ? (
                    <div className="mt-4">
                      <div className="flex justify-between text-sm text-gray-600 mb-1">
                        <span>Progress: {order.delivered.toLocaleString()} / {order.quantity.toLocaleString()}</span>
                        <span>{getProgress(order.delivered, order.quantity)}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-purple-600 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${getProgress(order.delivered, order.quantity)}%` }}
                        ></div>
                      </div>
                    </div>
                  ) : null}

                  {/* Order Details */}
                  <div className="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <p className="text-gray-600">Start Count</p>
                      <p className="font-medium">{order.startCount.toLocaleString()}</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Delivered</p>
                      <p className="font-medium">{order.delivered.toLocaleString()}</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Remains</p>
                      <p className="font-medium">{order.remains.toLocaleString()}</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Speed</p>
                      <p className="font-medium">{order.speed}</p>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="mt-4 flex flex-wrap gap-2">
                    <button className="text-purple-600 hover:text-purple-700 text-sm font-medium">
                      View Details
                    </button>
                    {order.status === 'pending' && (
                      <button className="text-red-600 hover:text-red-700 text-sm font-medium">
                        Cancel Order
                      </button>
                    )}
                    {(order.status === 'completed' || order.status === 'partial') && (
                      <button className="text-blue-600 hover:text-blue-700 text-sm font-medium">
                        Request Refill
                      </button>
                    )}
                    {order.status === 'completed' && (
                      <button className="text-green-600 hover:text-green-700 text-sm font-medium">
                        Reorder
                      </button>
                    )}
                  </div>
                </div>
              </div>
            ))
          )}
        </div>

        {/* Pagination */}
        {filteredOrders.length > 0 && (
          <div className="mt-8 flex justify-center">
            <nav className="flex items-center space-x-2">
              <button className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                Previous
              </button>
              <button className="px-3 py-2 text-sm font-medium text-white bg-purple-600 border border-purple-600 rounded-md">
                1
              </button>
              <button className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                2
              </button>
              <button className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                3
              </button>
              <button className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                Next
              </button>
            </nav>
          </div>
        )}
      </div>
    </div>
  );
}
