{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/trendz/frontend/src/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { useLanguage } from '../contexts/LanguageContext';\nimport { useTheme } from '../contexts/ThemeContext';\n\ninterface HeaderProps {\n  currentPage?: string;\n  isAdmin?: boolean;\n  user?: {\n    name: string;\n    balance?: number;\n  } | null;\n}\n\nexport default function Header({ currentPage, isAdmin = false, user = null }: HeaderProps) {\n  const { language, setLanguage, t, isRTL } = useLanguage();\n  const { theme, setTheme, isDark } = useTheme();\n  const [showUserMenu, setShowUserMenu] = useState(false);\n  const [showLanguageMenu, setShowLanguageMenu] = useState(false);\n  const [showThemeMenu, setShowThemeMenu] = useState(false);\n\n  const navigation = isAdmin ? [\n    { name: t('admin.title'), href: '/admin', current: currentPage === 'admin' },\n    { name: t('admin.userManagement'), href: '/admin/users', current: currentPage === 'users' },\n    { name: t('admin.orderManagement'), href: '/admin/orders', current: currentPage === 'orders' },\n    { name: t('admin.serviceManagement'), href: '/admin/services', current: currentPage === 'services' },\n    { name: t('admin.paymentManagement'), href: '/admin/payments', current: currentPage === 'payments' },\n    { name: t('admin.settings'), href: '/admin/settings', current: currentPage === 'settings' }\n  ] : [\n    { name: t('navigation.services'), href: '/services', current: currentPage === 'services' },\n    { name: t('navigation.faq'), href: '/faq', current: currentPage === 'faq' },\n    { name: t('navigation.support'), href: '/support', current: currentPage === 'support' },\n    { name: t('navigation.dashboard'), href: '/dashboard', current: currentPage === 'dashboard' }\n  ];\n\n  const userNavigation = user ? [\n    { name: t('navigation.dashboard'), href: '/dashboard' },\n    { name: t('navigation.orders'), href: '/orders' },\n    { name: t('navigation.payments'), href: '/payments' },\n    { name: t('navigation.profile'), href: '/profile' },\n    { name: t('navigation.logout'), href: '/auth/logout' }\n  ] : [\n    { name: t('navigation.login'), href: '/auth/login' },\n    { name: t('navigation.register'), href: '/auth/register' }\n  ];\n\n  return (\n    <div className={`bg-white dark:bg-gray-900 shadow ${isRTL ? 'rtl' : 'ltr'}`}>\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center py-6\">\n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"flex items-center space-x-2 rtl:space-x-reverse\">\n              <div className=\"w-8 h-8 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-sm\">T</span>\n              </div>\n              <span className=\"text-xl font-bold text-gray-900 dark:text-white\">\n                Trendz {isAdmin && t('navigation.admin')}\n              </span>\n            </Link>\n          </div>\n          \n          {/* Navigation */}\n          <nav className=\"hidden md:flex space-x-8 rtl:space-x-reverse\">\n            {navigation.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className={`${\n                  item.current\n                    ? 'text-purple-600 font-medium'\n                    : 'text-gray-600 dark:text-gray-300 hover:text-purple-600 dark:hover:text-purple-400'\n                } transition-colors`}\n              >\n                {item.name}\n              </Link>\n            ))}\n          </nav>\n\n          {/* Right side controls */}\n          <div className=\"flex items-center space-x-4 rtl:space-x-reverse\">\n            {/* Theme Toggle */}\n            <div className=\"relative\">\n              <button\n                onClick={() => setShowThemeMenu(!showThemeMenu)}\n                className=\"p-2 text-gray-600 dark:text-gray-300 hover:text-purple-600 dark:hover:text-purple-400 transition-colors\"\n                title={t('theme.light')}\n              >\n                {isDark ? (\n                  <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z\" />\n                  </svg>\n                ) : (\n                  <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z\" />\n                  </svg>\n                )}\n              </button>\n              \n              {showThemeMenu && (\n                <div className={`absolute ${isRTL ? 'left-0' : 'right-0'} mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg py-1 z-50`}>\n                  <button\n                    onClick={() => { setTheme('light'); setShowThemeMenu(false); }}\n                    className={`block w-full text-left px-4 py-2 text-sm ${theme === 'light' ? 'bg-purple-50 dark:bg-purple-900 text-purple-600 dark:text-purple-400' : 'text-gray-700 dark:text-gray-300'} hover:bg-gray-100 dark:hover:bg-gray-700`}\n                  >\n                    <div className=\"flex items-center\">\n                      <svg className=\"w-4 h-4 mr-2 rtl:ml-2 rtl:mr-0\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z\" />\n                      </svg>\n                      {t('theme.light')}\n                    </div>\n                  </button>\n                  <button\n                    onClick={() => { setTheme('dark'); setShowThemeMenu(false); }}\n                    className={`block w-full text-left px-4 py-2 text-sm ${theme === 'dark' ? 'bg-purple-50 dark:bg-purple-900 text-purple-600 dark:text-purple-400' : 'text-gray-700 dark:text-gray-300'} hover:bg-gray-100 dark:hover:bg-gray-700`}\n                  >\n                    <div className=\"flex items-center\">\n                      <svg className=\"w-4 h-4 mr-2 rtl:ml-2 rtl:mr-0\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z\" />\n                      </svg>\n                      {t('theme.dark')}\n                    </div>\n                  </button>\n                  <button\n                    onClick={() => { setTheme('system'); setShowThemeMenu(false); }}\n                    className={`block w-full text-left px-4 py-2 text-sm ${theme === 'system' ? 'bg-purple-50 dark:bg-purple-900 text-purple-600 dark:text-purple-400' : 'text-gray-700 dark:text-gray-300'} hover:bg-gray-100 dark:hover:bg-gray-700`}\n                  >\n                    <div className=\"flex items-center\">\n                      <svg className=\"w-4 h-4 mr-2 rtl:ml-2 rtl:mr-0\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\n                      </svg>\n                      {t('theme.system')}\n                    </div>\n                  </button>\n                </div>\n              )}\n            </div>\n\n            {/* Language Toggle */}\n            <div className=\"relative\">\n              <button\n                onClick={() => setShowLanguageMenu(!showLanguageMenu)}\n                className=\"flex items-center space-x-1 rtl:space-x-reverse text-gray-600 dark:text-gray-300 hover:text-purple-600 dark:hover:text-purple-400 transition-colors\"\n              >\n                <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129\" />\n                </svg>\n                <span className=\"text-sm font-medium\">\n                  {language === 'ar' ? 'ع' : 'EN'}\n                </span>\n              </button>\n              \n              {showLanguageMenu && (\n                <div className={`absolute ${isRTL ? 'left-0' : 'right-0'} mt-2 w-32 bg-white dark:bg-gray-800 rounded-md shadow-lg py-1 z-50`}>\n                  <button\n                    onClick={() => { setLanguage('en'); setShowLanguageMenu(false); }}\n                    className={`block w-full text-left px-4 py-2 text-sm ${language === 'en' ? 'bg-purple-50 dark:bg-purple-900 text-purple-600 dark:text-purple-400' : 'text-gray-700 dark:text-gray-300'} hover:bg-gray-100 dark:hover:bg-gray-700`}\n                  >\n                    {t('language.english')}\n                  </button>\n                  <button\n                    onClick={() => { setLanguage('ar'); setShowLanguageMenu(false); }}\n                    className={`block w-full text-left px-4 py-2 text-sm ${language === 'ar' ? 'bg-purple-50 dark:bg-purple-900 text-purple-600 dark:text-purple-400' : 'text-gray-700 dark:text-gray-300'} hover:bg-gray-100 dark:hover:bg-gray-700`}\n                  >\n                    {t('language.arabic')}\n                  </button>\n                </div>\n              )}\n            </div>\n\n            {/* User Menu */}\n            {user ? (\n              <div className=\"relative flex items-center\">\n                {user.balance !== undefined && (\n                  <div className=\"bg-gray-100 dark:bg-gray-700 px-3 py-1 rounded-full mr-2 rtl:ml-2 rtl:mr-0\">\n                    <span className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                      ${user.balance.toFixed(2)}\n                    </span>\n                  </div>\n                )}\n                <button\n                  onClick={() => setShowUserMenu(!showUserMenu)}\n                  className=\"w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center\"\n                >\n                  <span className=\"text-white text-sm font-medium\">\n                    {user.name.charAt(0).toUpperCase()}\n                  </span>\n                </button>\n\n                {showUserMenu && (\n                  <div className={`absolute ${isRTL ? 'left-0' : 'right-0'} mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg py-1 z-50`}>\n                    {userNavigation.map((item) => (\n                      <Link\n                        key={item.name}\n                        href={item.href}\n                        className=\"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\"\n                        onClick={() => setShowUserMenu(false)}\n                      >\n                        {item.name}\n                      </Link>\n                    ))}\n                  </div>\n                )}\n              </div>\n            ) : (\n              <div className=\"flex items-center space-x-4 rtl:space-x-reverse\">\n                <Link\n                  href=\"/auth/login\"\n                  className=\"text-gray-600 dark:text-gray-300 hover:text-purple-600 dark:hover:text-purple-400\"\n                >\n                  {t('navigation.login')}\n                </Link>\n                <Link\n                  href=\"/auth/register\"\n                  className=\"bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 transition-colors\"\n                >\n                  {t('navigation.register')}\n                </Link>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAgBe,SAAS,OAAO,EAAE,WAAW,EAAE,UAAU,KAAK,EAAE,OAAO,IAAI,EAAe;IACvF,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,CAAC,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,cAAW,AAAD;IACtD,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,aAAa,UAAU;QAC3B;YAAE,MAAM,EAAE;YAAgB,MAAM;YAAU,SAAS,gBAAgB;QAAQ;QAC3E;YAAE,MAAM,EAAE;YAAyB,MAAM;YAAgB,SAAS,gBAAgB;QAAQ;QAC1F;YAAE,MAAM,EAAE;YAA0B,MAAM;YAAiB,SAAS,gBAAgB;QAAS;QAC7F;YAAE,MAAM,EAAE;YAA4B,MAAM;YAAmB,SAAS,gBAAgB;QAAW;QACnG;YAAE,MAAM,EAAE;YAA4B,MAAM;YAAmB,SAAS,gBAAgB;QAAW;QACnG;YAAE,MAAM,EAAE;YAAmB,MAAM;YAAmB,SAAS,gBAAgB;QAAW;KAC3F,GAAG;QACF;YAAE,MAAM,EAAE;YAAwB,MAAM;YAAa,SAAS,gBAAgB;QAAW;QACzF;YAAE,MAAM,EAAE;YAAmB,MAAM;YAAQ,SAAS,gBAAgB;QAAM;QAC1E;YAAE,MAAM,EAAE;YAAuB,MAAM;YAAY,SAAS,gBAAgB;QAAU;QACtF;YAAE,MAAM,EAAE;YAAyB,MAAM;YAAc,SAAS,gBAAgB;QAAY;KAC7F;IAED,MAAM,iBAAiB,OAAO;QAC5B;YAAE,MAAM,EAAE;YAAyB,MAAM;QAAa;QACtD;YAAE,MAAM,EAAE;YAAsB,MAAM;QAAU;QAChD;YAAE,MAAM,EAAE;YAAwB,MAAM;QAAY;QACpD;YAAE,MAAM,EAAE;YAAuB,MAAM;QAAW;QAClD;YAAE,MAAM,EAAE;YAAsB,MAAM;QAAe;KACtD,GAAG;QACF;YAAE,MAAM,EAAE;YAAqB,MAAM;QAAc;QACnD;YAAE,MAAM,EAAE;YAAwB,MAAM;QAAiB;KAC1D;IAED,qBACE,8OAAC;QAAI,WAAW,CAAC,iCAAiC,EAAE,QAAQ,QAAQ,OAAO;kBACzE,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDAA+B;;;;;;;;;;;8CAEjD,8OAAC;oCAAK,WAAU;;wCAAkD;wCACxD,WAAW,EAAE;;;;;;;;;;;;;;;;;;kCAM3B,8OAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,WAAW,GACT,KAAK,OAAO,GACR,gCACA,oFACL,kBAAkB,CAAC;0CAEnB,KAAK,IAAI;+BARL,KAAK,IAAI;;;;;;;;;;kCAcpB,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS,IAAM,iBAAiB,CAAC;wCACjC,WAAU;wCACV,OAAO,EAAE;kDAER,uBACC,8OAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjE,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;iEAGvE,8OAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjE,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;oCAK1E,+BACC,8OAAC;wCAAI,WAAW,CAAC,SAAS,EAAE,QAAQ,WAAW,UAAU,mEAAmE,CAAC;;0DAC3H,8OAAC;gDACC,SAAS;oDAAQ,SAAS;oDAAU,iBAAiB;gDAAQ;gDAC7D,WAAW,CAAC,yCAAyC,EAAE,UAAU,UAAU,yEAAyE,mCAAmC,yCAAyC,CAAC;0DAEjO,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;4DAAiC,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEACxF,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;wDAEtE,EAAE;;;;;;;;;;;;0DAGP,8OAAC;gDACC,SAAS;oDAAQ,SAAS;oDAAS,iBAAiB;gDAAQ;gDAC5D,WAAW,CAAC,yCAAyC,EAAE,UAAU,SAAS,yEAAyE,mCAAmC,yCAAyC,CAAC;0DAEhO,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;4DAAiC,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEACxF,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;wDAEtE,EAAE;;;;;;;;;;;;0DAGP,8OAAC;gDACC,SAAS;oDAAQ,SAAS;oDAAW,iBAAiB;gDAAQ;gDAC9D,WAAW,CAAC,yCAAyC,EAAE,UAAU,WAAW,yEAAyE,mCAAmC,yCAAyC,CAAC;0DAElO,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;4DAAiC,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEACxF,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;wDAEtE,EAAE;;;;;;;;;;;;;;;;;;;;;;;;0CAQb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS,IAAM,oBAAoB,CAAC;wCACpC,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACjE,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;0DAEvE,8OAAC;gDAAK,WAAU;0DACb,aAAa,OAAO,MAAM;;;;;;;;;;;;oCAI9B,kCACC,8OAAC;wCAAI,WAAW,CAAC,SAAS,EAAE,QAAQ,WAAW,UAAU,mEAAmE,CAAC;;0DAC3H,8OAAC;gDACC,SAAS;oDAAQ,YAAY;oDAAO,oBAAoB;gDAAQ;gDAChE,WAAW,CAAC,yCAAyC,EAAE,aAAa,OAAO,yEAAyE,mCAAmC,yCAAyC,CAAC;0DAEhO,EAAE;;;;;;0DAEL,8OAAC;gDACC,SAAS;oDAAQ,YAAY;oDAAO,oBAAoB;gDAAQ;gDAChE,WAAW,CAAC,yCAAyC,EAAE,aAAa,OAAO,yEAAyE,mCAAmC,yCAAyC,CAAC;0DAEhO,EAAE;;;;;;;;;;;;;;;;;;4BAOV,qBACC,8OAAC;gCAAI,WAAU;;oCACZ,KAAK,OAAO,KAAK,2BAChB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;;gDAAoD;gDAChE,KAAK,OAAO,CAAC,OAAO,CAAC;;;;;;;;;;;;kDAI7B,8OAAC;wCACC,SAAS,IAAM,gBAAgB,CAAC;wCAChC,WAAU;kDAEV,cAAA,8OAAC;4CAAK,WAAU;sDACb,KAAK,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW;;;;;;;;;;;oCAInC,8BACC,8OAAC;wCAAI,WAAW,CAAC,SAAS,EAAE,QAAQ,WAAW,UAAU,mEAAmE,CAAC;kDAC1H,eAAe,GAAG,CAAC,CAAC,qBACnB,8OAAC,4JAAA,CAAA,UAAI;gDAEH,MAAM,KAAK,IAAI;gDACf,WAAU;gDACV,SAAS,IAAM,gBAAgB;0DAE9B,KAAK,IAAI;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;qDAYxB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDAET,EAAE;;;;;;kDAEL,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDAET,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASrB", "debugId": null}}, {"offset": {"line": 560, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/trendz/frontend/src/components/Footer.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { useLanguage } from '../contexts/LanguageContext';\n\nexport default function Footer() {\n  const { t, isRTL } = useLanguage();\n\n  return (\n    <footer className={`bg-gray-900 text-white py-12 ${isRTL ? 'rtl' : 'ltr'}`}>\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n          <div>\n            <div className=\"flex items-center space-x-2 rtl:space-x-reverse mb-4\">\n              <div className=\"w-8 h-8 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-sm\">T</span>\n              </div>\n              <span className=\"text-xl font-bold\">Trendz</span>\n            </div>\n            <p className=\"text-gray-400\">{t('footer.description')}</p>\n          </div>\n          \n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">{t('navigation.services')}</h3>\n            <ul className=\"space-y-2 text-gray-400\">\n              <li><Link href=\"/services\" className=\"hover:text-white\">{t('navigation.services')}</Link></li>\n              <li><Link href=\"/order\" className=\"hover:text-white\">{t('orders.title')}</Link></li>\n              <li><Link href=\"/orders\" className=\"hover:text-white\">{t('orders.title')}</Link></li>\n            </ul>\n          </div>\n          \n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">{t('navigation.support')}</h3>\n            <ul className=\"space-y-2 text-gray-400\">\n              <li><Link href=\"/support\" className=\"hover:text-white\">{t('navigation.support')}</Link></li>\n              <li><Link href=\"/faq\" className=\"hover:text-white\">{t('navigation.faq')}</Link></li>\n              <li><Link href=\"/terms\" className=\"hover:text-white\">{t('footer.termsOfService')}</Link></li>\n              <li><Link href=\"/privacy\" className=\"hover:text-white\">{t('footer.privacyPolicy')}</Link></li>\n            </ul>\n          </div>\n          \n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">{t('navigation.profile')}</h3>\n            <ul className=\"space-y-2 text-gray-400\">\n              <li><Link href=\"/auth/login\" className=\"hover:text-white\">{t('navigation.login')}</Link></li>\n              <li><Link href=\"/auth/register\" className=\"hover:text-white\">{t('navigation.register')}</Link></li>\n              <li><Link href=\"/dashboard\" className=\"hover:text-white\">{t('navigation.dashboard')}</Link></li>\n              <li><Link href=\"/profile\" className=\"hover:text-white\">{t('navigation.profile')}</Link></li>\n            </ul>\n          </div>\n        </div>\n        \n        <div className=\"border-t border-gray-800 mt-8 pt-8 text-center text-gray-400\">\n          <p>&copy; 2025 Trendz SMM Panel. {t('footer.allRightsReserved')}.</p>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,cAAW,AAAD;IAE/B,qBACE,8OAAC;QAAO,WAAW,CAAC,6BAA6B,EAAE,QAAQ,QAAQ,OAAO;kBACxE,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;sDAEjD,8OAAC;4CAAK,WAAU;sDAAoB;;;;;;;;;;;;8CAEtC,8OAAC;oCAAE,WAAU;8CAAiB,EAAE;;;;;;;;;;;;sCAGlC,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA8B,EAAE;;;;;;8CAC9C,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAY,WAAU;0DAAoB,EAAE;;;;;;;;;;;sDAC3D,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAAoB,EAAE;;;;;;;;;;;sDACxD,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAU,WAAU;0DAAoB,EAAE;;;;;;;;;;;;;;;;;;;;;;;sCAI7D,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA8B,EAAE;;;;;;8CAC9C,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAoB,EAAE;;;;;;;;;;;sDAC1D,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAO,WAAU;0DAAoB,EAAE;;;;;;;;;;;sDACtD,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAAoB,EAAE;;;;;;;;;;;sDACxD,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAoB,EAAE;;;;;;;;;;;;;;;;;;;;;;;sCAI9D,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA8B,EAAE;;;;;;8CAC9C,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAc,WAAU;0DAAoB,EAAE;;;;;;;;;;;sDAC7D,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAiB,WAAU;0DAAoB,EAAE;;;;;;;;;;;sDAChE,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;0DAAoB,EAAE;;;;;;;;;;;sDAC5D,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAoB,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAKhE,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;;4BAAE;4BAA+B,EAAE;4BAA4B;;;;;;;;;;;;;;;;;;;;;;;AAK1E", "debugId": null}}, {"offset": {"line": 908, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/trendz/frontend/src/components/Layout.tsx"], "sourcesContent": ["'use client';\n\nimport { ReactNode } from 'react';\nimport Header from './Header';\nimport Footer from './Footer';\nimport { useLanguage } from '../contexts/LanguageContext';\n\ninterface LayoutProps {\n  children: ReactNode;\n  currentPage?: string;\n  isAdmin?: boolean;\n  user?: {\n    name: string;\n    balance?: number;\n  } | null;\n  showFooter?: boolean;\n}\n\nexport default function Layout({ \n  children, \n  currentPage, \n  isAdmin = false, \n  user = null, \n  showFooter = true \n}: LayoutProps) {\n  const { isRTL } = useLanguage();\n\n  return (\n    <div className={`min-h-screen bg-gray-50 dark:bg-gray-900 ${isRTL ? 'rtl' : 'ltr'}`}>\n      <Header currentPage={currentPage} isAdmin={isAdmin} user={user} />\n      <main className=\"flex-1\">\n        {children}\n      </main>\n      {showFooter && <Footer />}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AALA;;;;;AAkBe,SAAS,OAAO,EAC7B,QAAQ,EACR,WAAW,EACX,UAAU,KAAK,EACf,OAAO,IAAI,EACX,aAAa,IAAI,EACL;IACZ,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,cAAW,AAAD;IAE5B,qBACE,8OAAC;QAAI,WAAW,CAAC,yCAAyC,EAAE,QAAQ,QAAQ,OAAO;;0BACjF,8OAAC,4HAAA,CAAA,UAAM;gBAAC,aAAa;gBAAa,SAAS;gBAAS,MAAM;;;;;;0BAC1D,8OAAC;gBAAK,WAAU;0BACb;;;;;;YAEF,4BAAc,8OAAC,4HAAA,CAAA,UAAM;;;;;;;;;;;AAG5B", "debugId": null}}, {"offset": {"line": 958, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/trendz/frontend/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport Layout from '../components/Layout';\nimport { useLanguage } from '../contexts/LanguageContext';\n\nexport default function Home() {\n  const { t } = useLanguage();\n\n  return (\n    <Layout currentPage=\"home\" showFooter={true}>\n\n        {/* Hero Section */}\n        <div className=\"bg-gradient-to-br from-purple-600 via-blue-600 to-indigo-700 text-white\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20\">\n            <div className=\"text-center\">\n              <h1 className=\"text-4xl md:text-6xl font-bold mb-6\">\n                <span className=\"block\">Trendz SMM Panel</span>\n                <span className=\"block text-transparent bg-clip-text bg-gradient-to-r from-yellow-400 to-orange-500\">\n                  Professional Services\n                </span>\n              </h1>\n              <p className=\"text-xl text-purple-100 mb-8 max-w-3xl mx-auto\">\n                Boost your social media presence with our premium services for all major platforms. Fast, reliable, and affordable.\n              </p>\n              <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n                <Link href=\"/services\" className=\"bg-white text-purple-600 px-8 py-4 rounded-lg font-semibold text-lg hover:bg-gray-100 transition-colors\">\n                  {t('services.title')}\n                </Link>\n                <Link href=\"/auth/register\" className=\"border-2 border-white text-white px-8 py-4 rounded-lg font-semibold text-lg hover:bg-white hover:text-purple-600 transition-colors\">\n                  {t('navigation.register')}\n                </Link>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Features Section */}\n        <div className=\"py-16 bg-white dark:bg-gray-800\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <div className=\"text-center mb-12\">\n              <h2 className=\"text-3xl font-bold text-gray-900 dark:text-white mb-4\">Why Choose Trendz?</h2>\n              <p className=\"text-lg text-gray-600 dark:text-gray-300\">We provide the best social media marketing services with guaranteed results</p>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n              <div className=\"text-center p-6\">\n                <div className=\"w-16 h-16 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center mx-auto mb-4\">\n                  <svg className=\"w-8 h-8 text-purple-600 dark:text-purple-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n                  </svg>\n                </div>\n                <h3 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-2\">Fast Delivery</h3>\n                <p className=\"text-gray-600 dark:text-gray-300\">Most orders start within 1-6 hours and complete quickly with our automated systems.</p>\n              </div>\n\n              <div className=\"text-center p-6\">\n                <div className=\"w-16 h-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-4\">\n                  <svg className=\"w-8 h-8 text-green-600 dark:text-green-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                  </svg>\n                </div>\n                <h3 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-2\">High Quality</h3>\n                <p className=\"text-gray-600 dark:text-gray-300\">Real accounts and high-quality engagement that looks natural and authentic.</p>\n              </div>\n\n              <div className=\"text-center p-6\">\n                <div className=\"w-16 h-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mx-auto mb-4\">\n                  <svg className=\"w-8 h-8 text-blue-600 dark:text-blue-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 2.25a9.75 9.75 0 109.75 9.75A9.75 9.75 0 0012 2.25z\" />\n                  </svg>\n                </div>\n                <h3 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-2\">24/7 Support</h3>\n                <p className=\"text-gray-600 dark:text-gray-300\">Round-the-clock customer support to help you with any questions or issues.</p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Platforms Section */}\n        <div className=\"py-16 bg-gray-50 dark:bg-gray-900\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <div className=\"text-center mb-12\">\n              <h2 className=\"text-3xl font-bold text-gray-900 dark:text-white mb-4\">Supported Platforms</h2>\n              <p className=\"text-lg text-gray-600 dark:text-gray-300\">We support all major social media platforms</p>\n            </div>\n\n            <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6\">\n              {[\n                { name: 'Instagram', icon: '📷', color: 'bg-pink-500' },\n                { name: 'TikTok', icon: '🎵', color: 'bg-black' },\n                { name: 'YouTube', icon: '📺', color: 'bg-red-500' },\n                { name: 'Facebook', icon: '📘', color: 'bg-blue-500' },\n                { name: 'Twitter', icon: '🐦', color: 'bg-blue-400' },\n                { name: 'Snapchat', icon: '👻', color: 'bg-yellow-400' }\n              ].map((platform, index) => (\n                <div key={index} className=\"bg-white dark:bg-gray-800 p-6 rounded-lg shadow text-center hover:shadow-md transition-shadow\">\n                  <div className={`w-12 h-12 ${platform.color} rounded-lg flex items-center justify-center mx-auto mb-3`}>\n                    <span className=\"text-2xl\">{platform.icon}</span>\n                  </div>\n                  <h3 className=\"font-medium text-gray-900 dark:text-white\">{platform.name}</h3>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n\n        {/* CTA Section */}\n        <div className=\"bg-purple-600 py-16\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n            <h2 className=\"text-3xl font-bold text-white mb-4\">Ready to Boost Your Social Media?</h2>\n            <p className=\"text-xl text-purple-100 mb-8\">Join thousands of satisfied customers and start growing today!</p>\n            <Link href=\"/auth/register\" className=\"bg-white text-purple-600 px-8 py-4 rounded-lg font-semibold text-lg hover:bg-gray-100 transition-colors\">\n              {t('navigation.register')}\n            </Link>\n          </div>\n        </div>\n    </Layout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,cAAW,AAAD;IAExB,qBACE,8OAAC,4HAAA,CAAA,UAAM;QAAC,aAAY;QAAO,YAAY;;0BAGnC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAK,WAAU;kDAAQ;;;;;;kDACxB,8OAAC;wCAAK,WAAU;kDAAqF;;;;;;;;;;;;0CAIvG,8OAAC;gCAAE,WAAU;0CAAiD;;;;;;0CAG9D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAY,WAAU;kDAC9B,EAAE;;;;;;kDAEL,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAiB,WAAU;kDACnC,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwD;;;;;;8CACtE,8OAAC;oCAAE,WAAU;8CAA2C;;;;;;;;;;;;sCAG1D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAA+C,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACtG,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,8OAAC;4CAAG,WAAU;sDAA2D;;;;;;sDACzE,8OAAC;4CAAE,WAAU;sDAAmC;;;;;;;;;;;;8CAGlD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAA6C,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACpG,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,8OAAC;4CAAG,WAAU;sDAA2D;;;;;;sDACzE,8OAAC;4CAAE,WAAU;sDAAmC;;;;;;;;;;;;8CAGlD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAA2C,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAClG,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,8OAAC;4CAAG,WAAU;sDAA2D;;;;;;sDACzE,8OAAC;4CAAE,WAAU;sDAAmC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOxD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwD;;;;;;8CACtE,8OAAC;oCAAE,WAAU;8CAA2C;;;;;;;;;;;;sCAG1D,8OAAC;4BAAI,WAAU;sCACZ;gCACC;oCAAE,MAAM;oCAAa,MAAM;oCAAM,OAAO;gCAAc;gCACtD;oCAAE,MAAM;oCAAU,MAAM;oCAAM,OAAO;gCAAW;gCAChD;oCAAE,MAAM;oCAAW,MAAM;oCAAM,OAAO;gCAAa;gCACnD;oCAAE,MAAM;oCAAY,MAAM;oCAAM,OAAO;gCAAc;gCACrD;oCAAE,MAAM;oCAAW,MAAM;oCAAM,OAAO;gCAAc;gCACpD;oCAAE,MAAM;oCAAY,MAAM;oCAAM,OAAO;gCAAgB;6BACxD,CAAC,GAAG,CAAC,CAAC,UAAU,sBACf,8OAAC;oCAAgB,WAAU;;sDACzB,8OAAC;4CAAI,WAAW,CAAC,UAAU,EAAE,SAAS,KAAK,CAAC,yDAAyD,CAAC;sDACpG,cAAA,8OAAC;gDAAK,WAAU;0DAAY,SAAS,IAAI;;;;;;;;;;;sDAE3C,8OAAC;4CAAG,WAAU;sDAA6C,SAAS,IAAI;;;;;;;mCAJhE;;;;;;;;;;;;;;;;;;;;;0BAYlB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAqC;;;;;;sCACnD,8OAAC;4BAAE,WAAU;sCAA+B;;;;;;sCAC5C,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAiB,WAAU;sCACnC,EAAE;;;;;;;;;;;;;;;;;;;;;;;AAMjB", "debugId": null}}]}