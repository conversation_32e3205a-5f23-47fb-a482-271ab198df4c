'use client';

import { useState } from 'react';
import Layout from '../../../components/Layout';
import { useLanguage } from '../../../contexts/LanguageContext';

export default function AdminSettings() {
  const { t } = useLanguage();
  const [activeTab, setActiveTab] = useState('general');
  
  const [generalSettings, setGeneralSettings] = useState({
    siteName: 'Trendz SMM Panel',
    siteDescription: 'Professional SMM panel for all your social media marketing needs',
    siteUrl: 'https://trendz-smm.com',
    adminEmail: '<EMAIL>',
    supportEmail: '<EMAIL>',
    timezone: 'UTC',
    currency: 'USD',
    maintenanceMode: false,
    registrationEnabled: true,
    emailVerificationRequired: true
  });

  const [paymentSettings, setPaymentSettings] = useState({
    paypalEnabled: true,
    paypalClientId: 'your-paypal-client-id',
    paypalSecret: '••••••••••••••••',
    vodafoneCashEnabled: true,
    vodafoneCashNumber: '+20**********',
    instaPayEnabled: true,
    instaPayAccount: 'your-instapay-account',
    bankTransferEnabled: true,
    bankName: 'National Bank of Egypt',
    bankAccount: '**********',
    minDeposit: 5.00,
    maxDeposit: 1000.00,
    depositFee: 0.035
  });

  const [emailSettings, setEmailSettings] = useState({
    smtpHost: 'smtp.gmail.com',
    smtpPort: 587,
    smtpUsername: '<EMAIL>',
    smtpPassword: '••••••••••••••••',
    smtpEncryption: 'TLS',
    fromName: 'Trendz SMM Panel',
    fromEmail: '<EMAIL>',
    orderNotifications: true,
    paymentNotifications: true,
    systemNotifications: true
  });

  const [securitySettings, setSecuritySettings] = useState({
    twoFactorRequired: false,
    sessionTimeout: 24,
    maxLoginAttempts: 5,
    passwordMinLength: 8,
    passwordRequireSpecial: true,
    passwordRequireNumbers: true,
    passwordRequireUppercase: true,
    ipWhitelist: '',
    rateLimitEnabled: true,
    rateLimitRequests: 100,
    rateLimitWindow: 60
  });

  const handleSaveSettings = (section: string) => {
    console.log(`Saving ${section} settings`);
  };

  const tabs = [
    { id: 'general', name: 'General', icon: '⚙️' },
    { id: 'payment', name: 'Payment', icon: '💳' },
    { id: 'email', name: 'Email', icon: '📧' },
    { id: 'security', name: 'Security', icon: '🔒' },
    { id: 'api', name: 'API', icon: '🔌' },
    { id: 'backup', name: 'Backup', icon: '💾' }
  ];

  return (
    <Layout currentPage="settings" isAdmin={true}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">{t('admin.settings')}</h1>
          <p className="text-gray-600 dark:text-gray-300">Configure system settings and preferences</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
              <div className="p-6">
                <nav className="space-y-2">
                  {tabs.map((tab) => (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`w-full text-left px-3 py-2 rounded-md transition-colors ${
                        activeTab === tab.id
                          ? 'bg-purple-100 dark:bg-purple-900 text-purple-700 dark:text-purple-300'
                          : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700'
                      }`}
                    >
                      <div className="flex items-center">
                        <span className="text-lg mr-3 rtl:ml-3 rtl:mr-0">{tab.icon}</span>
                        <span className="font-medium">{tab.name}</span>
                      </div>
                    </button>
                  ))}
                </nav>
              </div>
            </div>
          </div>

          {/* Content */}
          <div className="lg:col-span-3">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
              <div className="p-6">
                {activeTab === 'general' && (
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-6">General Settings</h3>
                    <div className="space-y-6">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">Site Name</label>
                          <input
                            type="text"
                            value={generalSettings.siteName}
                            onChange={(e) => setGeneralSettings(prev => ({ ...prev, siteName: e.target.value }))}
                            className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 focus:outline-none focus:ring-purple-500 focus:border-purple-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">Site URL</label>
                          <input
                            type="url"
                            value={generalSettings.siteUrl}
                            onChange={(e) => setGeneralSettings(prev => ({ ...prev, siteUrl: e.target.value }))}
                            className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 focus:outline-none focus:ring-purple-500 focus:border-purple-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                          />
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">Site Description</label>
                        <textarea
                          value={generalSettings.siteDescription}
                          onChange={(e) => setGeneralSettings(prev => ({ ...prev, siteDescription: e.target.value }))}
                          rows={3}
                          className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 focus:outline-none focus:ring-purple-500 focus:border-purple-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                        />
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">Admin Email</label>
                          <input
                            type="email"
                            value={generalSettings.adminEmail}
                            onChange={(e) => setGeneralSettings(prev => ({ ...prev, adminEmail: e.target.value }))}
                            className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 focus:outline-none focus:ring-purple-500 focus:border-purple-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">Support Email</label>
                          <input
                            type="email"
                            value={generalSettings.supportEmail}
                            onChange={(e) => setGeneralSettings(prev => ({ ...prev, supportEmail: e.target.value }))}
                            className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 focus:outline-none focus:ring-purple-500 focus:border-purple-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                          />
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">Timezone</label>
                          <select
                            value={generalSettings.timezone}
                            onChange={(e) => setGeneralSettings(prev => ({ ...prev, timezone: e.target.value }))}
                            className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 focus:outline-none focus:ring-purple-500 focus:border-purple-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                          >
                            <option value="UTC">UTC</option>
                            <option value="Africa/Cairo">Cairo (GMT+2)</option>
                            <option value="Asia/Riyadh">Riyadh (GMT+3)</option>
                            <option value="Asia/Dubai">Dubai (GMT+4)</option>
                          </select>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">Currency</label>
                          <select
                            value={generalSettings.currency}
                            onChange={(e) => setGeneralSettings(prev => ({ ...prev, currency: e.target.value }))}
                            className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 focus:outline-none focus:ring-purple-500 focus:border-purple-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                          >
                            <option value="USD">USD</option>
                            <option value="EUR">EUR</option>
                            <option value="EGP">EGP</option>
                            <option value="SAR">SAR</option>
                          </select>
                        </div>
                      </div>

                      <div className="space-y-4">
                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            checked={generalSettings.maintenanceMode}
                            onChange={(e) => setGeneralSettings(prev => ({ ...prev, maintenanceMode: e.target.checked }))}
                            className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                          />
                          <label className="ml-2 rtl:mr-2 rtl:ml-0 block text-sm text-gray-900 dark:text-white">
                            Maintenance Mode
                          </label>
                        </div>
                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            checked={generalSettings.registrationEnabled}
                            onChange={(e) => setGeneralSettings(prev => ({ ...prev, registrationEnabled: e.target.checked }))}
                            className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                          />
                          <label className="ml-2 rtl:mr-2 rtl:ml-0 block text-sm text-gray-900 dark:text-white">
                            Allow New Registrations
                          </label>
                        </div>
                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            checked={generalSettings.emailVerificationRequired}
                            onChange={(e) => setGeneralSettings(prev => ({ ...prev, emailVerificationRequired: e.target.checked }))}
                            className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                          />
                          <label className="ml-2 rtl:mr-2 rtl:ml-0 block text-sm text-gray-900 dark:text-white">
                            Require Email Verification
                          </label>
                        </div>
                      </div>

                      <div className="pt-4">
                        <button
                          onClick={() => handleSaveSettings('general')}
                          className="bg-purple-600 text-white px-6 py-2 rounded-md hover:bg-purple-700 transition-colors"
                        >
                          {t('common.save')} Changes
                        </button>
                      </div>
                    </div>
                  </div>
                )}

                {activeTab === 'payment' && (
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-6">Payment Settings</h3>
                    <div className="space-y-8">
                      {/* PayPal Settings */}
                      <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-4">
                          <h4 className="text-md font-medium text-gray-900 dark:text-white">PayPal</h4>
                          <input
                            type="checkbox"
                            checked={paymentSettings.paypalEnabled}
                            onChange={(e) => setPaymentSettings(prev => ({ ...prev, paypalEnabled: e.target.checked }))}
                            className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                          />
                        </div>
                        {paymentSettings.paypalEnabled && (
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">Client ID</label>
                              <input
                                type="text"
                                value={paymentSettings.paypalClientId}
                                onChange={(e) => setPaymentSettings(prev => ({ ...prev, paypalClientId: e.target.value }))}
                                className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 focus:outline-none focus:ring-purple-500 focus:border-purple-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                              />
                            </div>
                            <div>
                              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">Client Secret</label>
                              <input
                                type="password"
                                value={paymentSettings.paypalSecret}
                                onChange={(e) => setPaymentSettings(prev => ({ ...prev, paypalSecret: e.target.value }))}
                                className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 focus:outline-none focus:ring-purple-500 focus:border-purple-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                              />
                            </div>
                          </div>
                        )}
                      </div>

                      {/* Vodafone Cash Settings */}
                      <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-4">
                          <h4 className="text-md font-medium text-gray-900 dark:text-white">Vodafone Cash</h4>
                          <input
                            type="checkbox"
                            checked={paymentSettings.vodafoneCashEnabled}
                            onChange={(e) => setPaymentSettings(prev => ({ ...prev, vodafoneCashEnabled: e.target.checked }))}
                            className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                          />
                        </div>
                        {paymentSettings.vodafoneCashEnabled && (
                          <div>
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">Phone Number</label>
                            <input
                              type="tel"
                              value={paymentSettings.vodafoneCashNumber}
                              onChange={(e) => setPaymentSettings(prev => ({ ...prev, vodafoneCashNumber: e.target.value }))}
                              className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 focus:outline-none focus:ring-purple-500 focus:border-purple-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                            />
                          </div>
                        )}
                      </div>

                      {/* General Payment Settings */}
                      <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                        <h4 className="text-md font-medium text-gray-900 dark:text-white mb-4">General Settings</h4>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">Min Deposit ($)</label>
                            <input
                              type="number"
                              value={paymentSettings.minDeposit}
                              onChange={(e) => setPaymentSettings(prev => ({ ...prev, minDeposit: parseFloat(e.target.value) }))}
                              step="0.01"
                              className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 focus:outline-none focus:ring-purple-500 focus:border-purple-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">Max Deposit ($)</label>
                            <input
                              type="number"
                              value={paymentSettings.maxDeposit}
                              onChange={(e) => setPaymentSettings(prev => ({ ...prev, maxDeposit: parseFloat(e.target.value) }))}
                              step="0.01"
                              className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 focus:outline-none focus:ring-purple-500 focus:border-purple-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">Deposit Fee (%)</label>
                            <input
                              type="number"
                              value={paymentSettings.depositFee * 100}
                              onChange={(e) => setPaymentSettings(prev => ({ ...prev, depositFee: parseFloat(e.target.value) / 100 }))}
                              step="0.1"
                              className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 focus:outline-none focus:ring-purple-500 focus:border-purple-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                            />
                          </div>
                        </div>
                      </div>

                      <div className="pt-4">
                        <button
                          onClick={() => handleSaveSettings('payment')}
                          className="bg-purple-600 text-white px-6 py-2 rounded-md hover:bg-purple-700 transition-colors"
                        >
                          {t('common.save')} Changes
                        </button>
                      </div>
                    </div>
                  </div>
                )}

                {activeTab === 'security' && (
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-6">Security Settings</h3>
                    <div className="space-y-6">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">Session Timeout (hours)</label>
                          <input
                            type="number"
                            value={securitySettings.sessionTimeout}
                            onChange={(e) => setSecuritySettings(prev => ({ ...prev, sessionTimeout: parseInt(e.target.value) }))}
                            className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 focus:outline-none focus:ring-purple-500 focus:border-purple-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">Max Login Attempts</label>
                          <input
                            type="number"
                            value={securitySettings.maxLoginAttempts}
                            onChange={(e) => setSecuritySettings(prev => ({ ...prev, maxLoginAttempts: parseInt(e.target.value) }))}
                            className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 focus:outline-none focus:ring-purple-500 focus:border-purple-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                          />
                        </div>
                      </div>

                      <div className="space-y-4">
                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            checked={securitySettings.twoFactorRequired}
                            onChange={(e) => setSecuritySettings(prev => ({ ...prev, twoFactorRequired: e.target.checked }))}
                            className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                          />
                          <label className="ml-2 rtl:mr-2 rtl:ml-0 block text-sm text-gray-900 dark:text-white">
                            Require Two-Factor Authentication
                          </label>
                        </div>
                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            checked={securitySettings.rateLimitEnabled}
                            onChange={(e) => setSecuritySettings(prev => ({ ...prev, rateLimitEnabled: e.target.checked }))}
                            className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                          />
                          <label className="ml-2 rtl:mr-2 rtl:ml-0 block text-sm text-gray-900 dark:text-white">
                            Enable Rate Limiting
                          </label>
                        </div>
                      </div>

                      <div className="pt-4">
                        <button
                          onClick={() => handleSaveSettings('security')}
                          className="bg-purple-600 text-white px-6 py-2 rounded-md hover:bg-purple-700 transition-colors"
                        >
                          {t('common.save')} Changes
                        </button>
                      </div>
                    </div>
                  </div>
                )}

                {(activeTab === 'email' || activeTab === 'api' || activeTab === 'backup') && (
                  <div className="text-center py-12">
                    <div className="text-4xl mb-4">🚧</div>
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">Coming Soon</h3>
                    <p className="text-gray-600 dark:text-gray-300">
                      This section is under development and will be available soon.
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
}
