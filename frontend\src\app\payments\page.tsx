'use client';

import { useState } from 'react';
import Link from 'next/link';

export default function Payments() {
  const [activeTab, setActiveTab] = useState('history');
  const [selectedMethod, setSelectedMethod] = useState('paypal');
  const [amount, setAmount] = useState('');

  const [user] = useState({
    balance: 125.50,
    totalDeposited: 500.00,
    totalSpent: 374.50
  });

  const paymentHistory = [
    {
      id: 'PAY001',
      type: 'deposit',
      method: 'PayPal',
      amount: 50.00,
      status: 'completed',
      date: '2025-01-30',
      transactionId: 'PP123456789'
    },
    {
      id: 'PAY002',
      type: 'order',
      method: 'Balance',
      amount: -12.50,
      status: 'completed',
      date: '2025-01-30',
      orderId: 'TRZ001'
    },
    {
      id: 'PAY003',
      type: 'deposit',
      method: 'Vodafone Cash',
      amount: 100.00,
      status: 'pending',
      date: '2025-01-29',
      transactionId: 'VC987654321'
    },
    {
      id: 'PAY004',
      type: 'order',
      method: 'Balance',
      amount: -8.75,
      status: 'completed',
      date: '2025-01-29',
      orderId: 'TRZ002'
    },
    {
      id: 'PAY005',
      type: 'deposit',
      method: 'InstaPay',
      amount: 75.00,
      status: 'completed',
      date: '2025-01-28',
      transactionId: 'IP456789123'
    }
  ];

  const paymentMethods = [
    {
      id: 'paypal',
      name: 'PayPal',
      icon: '💳',
      description: 'Pay securely with PayPal',
      fees: '3.5%',
      minAmount: 5,
      maxAmount: 1000,
      processingTime: 'Instant'
    },
    {
      id: 'vodafone_cash',
      name: 'Vodafone Cash',
      icon: '📱',
      description: 'Pay with Vodafone Cash',
      fees: 'Free',
      minAmount: 10,
      maxAmount: 500,
      processingTime: '5-15 minutes'
    },
    {
      id: 'instapay',
      name: 'InstaPay',
      icon: '🏦',
      description: 'Bank transfer via InstaPay',
      fees: 'Free',
      minAmount: 20,
      maxAmount: 1000,
      processingTime: '10-30 minutes'
    },
    {
      id: 'bank_transfer',
      name: 'Bank Transfer',
      icon: '🏛️',
      description: 'Direct bank transfer',
      fees: 'Free',
      minAmount: 50,
      maxAmount: 2000,
      processingTime: '1-3 hours'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'failed': return 'bg-red-100 text-red-800';
      case 'cancelled': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed': return 'مكتمل';
      case 'pending': return 'معلق';
      case 'failed': return 'فاشل';
      case 'cancelled': return 'ملغي';
      default: return status;
    }
  };

  const getTypeColor = (type: string) => {
    return type === 'deposit' ? 'text-green-600' : 'text-red-600';
  };

  const getTypeText = (type: string) => {
    return type === 'deposit' ? 'إيداع' : 'طلب';
  };

  const selectedMethodData = paymentMethods.find(method => method.id === selectedMethod);

  const handleAddFunds = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Adding funds:', { method: selectedMethod, amount });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <Link href="/" className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-sm">T</span>
                </div>
                <span className="text-xl font-bold text-gray-900">Trendz</span>
              </Link>
            </div>
            
            <nav className="hidden md:flex space-x-8">
              <Link href="/dashboard" className="text-gray-600 hover:text-purple-600">Dashboard</Link>
              <Link href="/services" className="text-gray-600 hover:text-purple-600">Services</Link>
              <Link href="/orders" className="text-gray-600 hover:text-purple-600">Orders</Link>
              <Link href="/payments" className="text-purple-600 font-medium">Payments</Link>
              <Link href="/profile" className="text-gray-600 hover:text-purple-600">Profile</Link>
            </nav>

            <div className="flex items-center space-x-4">
              <div className="bg-gray-100 px-3 py-1 rounded-full">
                <span className="text-sm font-medium">${user.balance.toFixed(2)}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-2xl font-bold text-gray-900">Payments & Billing</h1>
          <p className="text-gray-600">Manage your account balance and payment history</p>
        </div>

        {/* Balance Overview */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Current Balance</p>
                <p className="text-2xl font-bold text-gray-900">${user.balance.toFixed(2)}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16l-4-4m0 0l4-4m-4 4h18" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Deposited</p>
                <p className="text-2xl font-bold text-gray-900">${user.totalDeposited.toFixed(2)}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Spent</p>
                <p className="text-2xl font-bold text-gray-900">${user.totalSpent.toFixed(2)}</p>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Add Funds Section */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">Add Funds</h3>
              </div>
              <div className="p-6">
                <form onSubmit={handleAddFunds} className="space-y-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-3">Payment Method</label>
                    <div className="space-y-2">
                      {paymentMethods.map((method) => (
                        <div key={method.id} className="flex items-center">
                          <input
                            type="radio"
                            id={method.id}
                            name="paymentMethod"
                            value={method.id}
                            checked={selectedMethod === method.id}
                            onChange={(e) => setSelectedMethod(e.target.value)}
                            className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300"
                          />
                          <label htmlFor={method.id} className="ml-3 flex items-center cursor-pointer">
                            <span className="text-xl mr-2">{method.icon}</span>
                            <div>
                              <p className="text-sm font-medium text-gray-900">{method.name}</p>
                              <p className="text-xs text-gray-500">{method.description}</p>
                            </div>
                          </label>
                        </div>
                      ))}
                    </div>
                  </div>

                  {selectedMethodData && (
                    <div className="bg-gray-50 rounded-lg p-4">
                      <h4 className="text-sm font-medium text-gray-900 mb-2">Payment Details</h4>
                      <div className="space-y-1 text-sm text-gray-600">
                        <div className="flex justify-between">
                          <span>Fees:</span>
                          <span>{selectedMethodData.fees}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Min Amount:</span>
                          <span>${selectedMethodData.minAmount}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Max Amount:</span>
                          <span>${selectedMethodData.maxAmount}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Processing Time:</span>
                          <span>{selectedMethodData.processingTime}</span>
                        </div>
                      </div>
                    </div>
                  )}

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Amount (USD)</label>
                    <div className="mt-1 relative">
                      <span className="absolute left-3 top-2 text-gray-500">$</span>
                      <input
                        type="number"
                        value={amount}
                        onChange={(e) => setAmount(e.target.value)}
                        min={selectedMethodData?.minAmount}
                        max={selectedMethodData?.maxAmount}
                        step="0.01"
                        className="pl-8 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-purple-500 focus:border-purple-500"
                        placeholder="0.00"
                      />
                    </div>
                  </div>

                  <div className="flex space-x-2">
                    {[10, 25, 50, 100].map((preset) => (
                      <button
                        key={preset}
                        type="button"
                        onClick={() => setAmount(preset.toString())}
                        className="flex-1 px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-purple-500"
                      >
                        ${preset}
                      </button>
                    ))}
                  </div>

                  <button
                    type="submit"
                    className="w-full bg-purple-600 text-white py-2 px-4 rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 font-medium"
                  >
                    Add Funds
                  </button>
                </form>
              </div>
            </div>
          </div>

          {/* Payment History */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">Payment History</h3>
              </div>
              <div className="p-6">
                <div className="space-y-4">
                  {paymentHistory.map((payment) => (
                    <div key={payment.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <div className="flex items-center space-x-4">
                        <div className={`text-2xl ${payment.type === 'deposit' ? 'text-green-600' : 'text-red-600'}`}>
                          {payment.type === 'deposit' ? '💰' : '🛒'}
                        </div>
                        <div>
                          <div className="flex items-center space-x-2">
                            <h4 className="text-sm font-medium text-gray-900">
                              {getTypeText(payment.type)} - {payment.method}
                            </h4>
                            <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(payment.status)}`}>
                              {getStatusText(payment.status)}
                            </span>
                          </div>
                          <p className="text-sm text-gray-600">
                            {payment.type === 'deposit' ? `Transaction: ${payment.transactionId}` : `Order: ${payment.orderId}`}
                          </p>
                          <p className="text-sm text-gray-500">{payment.date}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className={`text-lg font-bold ${getTypeColor(payment.type)}`}>
                          {payment.amount > 0 ? '+' : ''}${Math.abs(payment.amount).toFixed(2)}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>

                <div className="mt-6 text-center">
                  <button className="text-purple-600 hover:text-purple-500 text-sm font-medium">
                    Load More Transactions
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
