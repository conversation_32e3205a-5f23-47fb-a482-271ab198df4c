{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/trendz/frontend/src/lib/api.ts"], "sourcesContent": ["import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';\nimport Cookies from 'js-cookie';\nimport toast from 'react-hot-toast';\n\n// Types\nimport type { \n  ApiResponse, \n  User, \n  Service, \n  Order, \n  Payment, \n  LoginForm, \n  RegisterForm, \n  OrderForm, \n  PaymentForm,\n  PaginatedResponse,\n  PaginationParams\n} from '@/types';\n\nclass ApiClient {\n  private client: AxiosInstance;\n  private baseURL: string;\n\n  constructor() {\n    this.baseURL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';\n    \n    this.client = axios.create({\n      baseURL: this.baseURL,\n      timeout: 30000,\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    });\n\n    this.setupInterceptors();\n  }\n\n  private setupInterceptors() {\n    // Request interceptor\n    this.client.interceptors.request.use(\n      (config) => {\n        const token = Cookies.get('token');\n        if (token) {\n          config.headers.Authorization = `Bearer ${token}`;\n        }\n        \n        // Add language header\n        const language = Cookies.get('i18next') || 'en';\n        config.headers['Accept-Language'] = language;\n        \n        return config;\n      },\n      (error) => {\n        return Promise.reject(error);\n      }\n    );\n\n    // Response interceptor\n    this.client.interceptors.response.use(\n      (response: AxiosResponse) => {\n        return response;\n      },\n      (error) => {\n        if (error.response?.status === 401) {\n          // Unauthorized - clear token and redirect to login\n          Cookies.remove('token');\n          if (typeof window !== 'undefined') {\n            window.location.href = '/auth/login';\n          }\n        } else if (error.response?.status >= 500) {\n          // Server error\n          toast.error('Server error. Please try again later.');\n        } else if (error.code === 'ECONNABORTED') {\n          // Timeout\n          toast.error('Request timeout. Please try again.');\n        } else if (!error.response) {\n          // Network error\n          toast.error('Network error. Please check your connection.');\n        }\n        \n        return Promise.reject(error);\n      }\n    );\n  }\n\n  // Generic request method\n  private async request<T>(config: AxiosRequestConfig): Promise<ApiResponse<T>> {\n    try {\n      const response = await this.client.request<ApiResponse<T>>(config);\n      return response.data;\n    } catch (error: any) {\n      const errorMessage = error.response?.data?.error || error.message || 'An error occurred';\n      throw new Error(errorMessage);\n    }\n  }\n\n  // Auth endpoints\n  async login(credentials: LoginForm): Promise<ApiResponse<{ token: string; user: User }>> {\n    return this.request({\n      method: 'POST',\n      url: '/auth/login',\n      data: credentials,\n    });\n  }\n\n  async register(data: RegisterForm): Promise<ApiResponse<{ token: string; user: User }>> {\n    return this.request({\n      method: 'POST',\n      url: '/auth/register',\n      data,\n    });\n  }\n\n  async logout(): Promise<ApiResponse> {\n    return this.request({\n      method: 'POST',\n      url: '/auth/logout',\n    });\n  }\n\n  async getMe(): Promise<ApiResponse<{ user: User }>> {\n    return this.request({\n      method: 'GET',\n      url: '/auth/me',\n    });\n  }\n\n  async updateProfile(data: Partial<User>): Promise<ApiResponse<{ user: User }>> {\n    return this.request({\n      method: 'PUT',\n      url: '/auth/profile',\n      data,\n    });\n  }\n\n  async changePassword(data: { currentPassword: string; newPassword: string }): Promise<ApiResponse> {\n    return this.request({\n      method: 'PUT',\n      url: '/auth/change-password',\n      data,\n    });\n  }\n\n  async forgotPassword(email: string): Promise<ApiResponse> {\n    return this.request({\n      method: 'POST',\n      url: '/auth/forgot-password',\n      data: { email },\n    });\n  }\n\n  async resetPassword(data: { token: string; password: string }): Promise<ApiResponse> {\n    return this.request({\n      method: 'POST',\n      url: '/auth/reset-password',\n      data,\n    });\n  }\n\n  // Services endpoints\n  async getServices(params?: PaginationParams & {\n    platform?: string;\n    category?: string;\n    featured?: boolean;\n    q?: string;\n  }): Promise<ApiResponse<PaginatedResponse<Service>>> {\n    return this.request({\n      method: 'GET',\n      url: '/services',\n      params,\n    });\n  }\n\n  async getService(id: string): Promise<ApiResponse<{ service: Service }>> {\n    return this.request({\n      method: 'GET',\n      url: `/services/${id}`,\n    });\n  }\n\n  async calculatePrice(data: { serviceId: string; quantity: number }): Promise<ApiResponse<{ price: number }>> {\n    return this.request({\n      method: 'POST',\n      url: '/services/calculate-price',\n      data,\n    });\n  }\n\n  // Orders endpoints\n  async createOrder(data: OrderForm): Promise<ApiResponse<{ order: Order }>> {\n    return this.request({\n      method: 'POST',\n      url: '/orders',\n      data,\n    });\n  }\n\n  async getOrders(params?: PaginationParams & {\n    status?: string;\n    serviceId?: string;\n    platform?: string;\n    startDate?: string;\n    endDate?: string;\n  }): Promise<ApiResponse<PaginatedResponse<Order>>> {\n    return this.request({\n      method: 'GET',\n      url: '/orders',\n      params,\n    });\n  }\n\n  async getOrder(id: string): Promise<ApiResponse<{ order: Order }>> {\n    return this.request({\n      method: 'GET',\n      url: `/orders/${id}`,\n    });\n  }\n\n  async cancelOrder(id: string, reason?: string): Promise<ApiResponse> {\n    return this.request({\n      method: 'PATCH',\n      url: `/orders/${id}/cancel`,\n      data: { reason },\n    });\n  }\n\n  async requestRefill(id: string, reason?: string): Promise<ApiResponse> {\n    return this.request({\n      method: 'PATCH',\n      url: `/orders/${id}/refill`,\n      data: { reason },\n    });\n  }\n\n  async getOrderStatistics(params?: { startDate?: string; endDate?: string }): Promise<ApiResponse<any>> {\n    return this.request({\n      method: 'GET',\n      url: '/orders/statistics',\n      params,\n    });\n  }\n\n  // Payments endpoints\n  async createPayment(data: PaymentForm): Promise<ApiResponse<{ payment: Payment }>> {\n    return this.request({\n      method: 'POST',\n      url: '/payments',\n      data,\n    });\n  }\n\n  async getPayments(params?: PaginationParams & {\n    status?: string;\n    method?: string;\n    type?: string;\n    startDate?: string;\n    endDate?: string;\n  }): Promise<ApiResponse<PaginatedResponse<Payment>>> {\n    return this.request({\n      method: 'GET',\n      url: '/payments',\n      params,\n    });\n  }\n\n  async getPayment(id: string): Promise<ApiResponse<{ payment: Payment }>> {\n    return this.request({\n      method: 'GET',\n      url: `/payments/${id}`,\n    });\n  }\n\n  async confirmManualPayment(id: string, details: any): Promise<ApiResponse> {\n    return this.request({\n      method: 'PATCH',\n      url: `/payments/${id}/confirm`,\n      data: details,\n    });\n  }\n\n  async getPaymentMethods(): Promise<ApiResponse<any>> {\n    return this.request({\n      method: 'GET',\n      url: '/payments/methods',\n    });\n  }\n\n  async getPaymentStatistics(params?: { startDate?: string; endDate?: string }): Promise<ApiResponse<any>> {\n    return this.request({\n      method: 'GET',\n      url: '/payments/statistics',\n      params,\n    });\n  }\n\n  // PayPal endpoints\n  async createPaypalOrder(data: { amount: number; currency?: string }): Promise<ApiResponse<{ orderID: string }>> {\n    return this.request({\n      method: 'POST',\n      url: '/payments/paypal/create-order',\n      data,\n    });\n  }\n\n  async capturePaypalOrder(orderID: string): Promise<ApiResponse> {\n    return this.request({\n      method: 'POST',\n      url: `/payments/paypal/capture-order/${orderID}`,\n    });\n  }\n\n  // User endpoints\n  async getUserBalance(): Promise<ApiResponse<{ balance: number }>> {\n    return this.request({\n      method: 'GET',\n      url: '/users/balance',\n    });\n  }\n\n  async getUserOrders(params?: PaginationParams): Promise<ApiResponse<PaginatedResponse<Order>>> {\n    return this.request({\n      method: 'GET',\n      url: '/users/orders',\n      params,\n    });\n  }\n\n  async getUserPayments(params?: PaginationParams): Promise<ApiResponse<PaginatedResponse<Payment>>> {\n    return this.request({\n      method: 'GET',\n      url: '/users/payments',\n      params,\n    });\n  }\n\n  async getUserStatistics(params?: { startDate?: string; endDate?: string }): Promise<ApiResponse<any>> {\n    return this.request({\n      method: 'GET',\n      url: '/users/statistics',\n      params,\n    });\n  }\n\n  async updateNotificationSettings(settings: any): Promise<ApiResponse> {\n    return this.request({\n      method: 'PUT',\n      url: '/users/notifications',\n      data: settings,\n    });\n  }\n\n  async uploadAvatar(file: File): Promise<ApiResponse<{ avatar: string }>> {\n    const formData = new FormData();\n    formData.append('avatar', file);\n\n    return this.request({\n      method: 'POST',\n      url: '/users/avatar',\n      data: formData,\n      headers: {\n        'Content-Type': 'multipart/form-data',\n      },\n    });\n  }\n\n  async deleteAvatar(): Promise<ApiResponse> {\n    return this.request({\n      method: 'DELETE',\n      url: '/users/avatar',\n    });\n  }\n}\n\n// Create and export a singleton instance\nconst apiClient = new ApiClient();\nexport default apiClient;\n"], "names": [], "mappings": ";;;AAwBmB;;AAxBnB;AACA;AACA;;;;;AAiBA,MAAM;IAkBI,oBAAoB;QAC1B,sBAAsB;QACtB,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CAClC,CAAC;YACC,MAAM,QAAQ,wJAAA,CAAA,UAAO,CAAC,GAAG,CAAC;YAC1B,IAAI,OAAO;gBACT,OAAO,OAAO,CAAC,aAAa,GAAG,AAAC,UAAe,OAAN;YAC3C;YAEA,sBAAsB;YACtB,MAAM,WAAW,wJAAA,CAAA,UAAO,CAAC,GAAG,CAAC,cAAc;YAC3C,OAAO,OAAO,CAAC,kBAAkB,GAAG;YAEpC,OAAO;QACT,GACA,CAAC;YACC,OAAO,QAAQ,MAAM,CAAC;QACxB;QAGF,uBAAuB;QACvB,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CACnC,CAAC;YACC,OAAO;QACT,GACA,CAAC;gBACK,iBAMO;YANX,IAAI,EAAA,kBAAA,MAAM,QAAQ,cAAd,sCAAA,gBAAgB,MAAM,MAAK,KAAK;gBAClC,mDAAmD;gBACnD,wJAAA,CAAA,UAAO,CAAC,MAAM,CAAC;gBACf,wCAAmC;oBACjC,OAAO,QAAQ,CAAC,IAAI,GAAG;gBACzB;YACF,OAAO,IAAI,EAAA,mBAAA,MAAM,QAAQ,cAAd,uCAAA,iBAAgB,MAAM,KAAI,KAAK;gBACxC,eAAe;gBACf,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACd,OAAO,IAAI,MAAM,IAAI,KAAK,gBAAgB;gBACxC,UAAU;gBACV,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACd,OAAO,IAAI,CAAC,MAAM,QAAQ,EAAE;gBAC1B,gBAAgB;gBAChB,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACd;YAEA,OAAO,QAAQ,MAAM,CAAC;QACxB;IAEJ;IAEA,yBAAyB;IACzB,MAAc,QAAW,MAA0B,EAA2B;QAC5E,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAiB;YAC3D,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAY;gBACE,sBAAA;YAArB,MAAM,eAAe,EAAA,kBAAA,MAAM,QAAQ,cAAd,uCAAA,uBAAA,gBAAgB,IAAI,cAApB,2CAAA,qBAAsB,KAAK,KAAI,MAAM,OAAO,IAAI;YACrE,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,iBAAiB;IACjB,MAAM,MAAM,WAAsB,EAAuD;QACvF,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,QAAQ;YACR,KAAK;YACL,MAAM;QACR;IACF;IAEA,MAAM,SAAS,IAAkB,EAAuD;QACtF,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,QAAQ;YACR,KAAK;YACL;QACF;IACF;IAEA,MAAM,SAA+B;QACnC,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,QAAQ;YACR,KAAK;QACP;IACF;IAEA,MAAM,QAA8C;QAClD,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,QAAQ;YACR,KAAK;QACP;IACF;IAEA,MAAM,cAAc,IAAmB,EAAwC;QAC7E,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,QAAQ;YACR,KAAK;YACL;QACF;IACF;IAEA,MAAM,eAAe,IAAsD,EAAwB;QACjG,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,QAAQ;YACR,KAAK;YACL;QACF;IACF;IAEA,MAAM,eAAe,KAAa,EAAwB;QACxD,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,QAAQ;YACR,KAAK;YACL,MAAM;gBAAE;YAAM;QAChB;IACF;IAEA,MAAM,cAAc,IAAyC,EAAwB;QACnF,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,QAAQ;YACR,KAAK;YACL;QACF;IACF;IAEA,qBAAqB;IACrB,MAAM,YAAY,MAKjB,EAAoD;QACnD,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,QAAQ;YACR,KAAK;YACL;QACF;IACF;IAEA,MAAM,WAAW,EAAU,EAA8C;QACvE,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,QAAQ;YACR,KAAK,AAAC,aAAe,OAAH;QACpB;IACF;IAEA,MAAM,eAAe,IAA6C,EAA2C;QAC3G,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,QAAQ;YACR,KAAK;YACL;QACF;IACF;IAEA,mBAAmB;IACnB,MAAM,YAAY,IAAe,EAA0C;QACzE,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,QAAQ;YACR,KAAK;YACL;QACF;IACF;IAEA,MAAM,UAAU,MAMf,EAAkD;QACjD,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,QAAQ;YACR,KAAK;YACL;QACF;IACF;IAEA,MAAM,SAAS,EAAU,EAA0C;QACjE,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,QAAQ;YACR,KAAK,AAAC,WAAa,OAAH;QAClB;IACF;IAEA,MAAM,YAAY,EAAU,EAAE,MAAe,EAAwB;QACnE,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,QAAQ;YACR,KAAK,AAAC,WAAa,OAAH,IAAG;YACnB,MAAM;gBAAE;YAAO;QACjB;IACF;IAEA,MAAM,cAAc,EAAU,EAAE,MAAe,EAAwB;QACrE,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,QAAQ;YACR,KAAK,AAAC,WAAa,OAAH,IAAG;YACnB,MAAM;gBAAE;YAAO;QACjB;IACF;IAEA,MAAM,mBAAmB,MAAiD,EAA6B;QACrG,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,QAAQ;YACR,KAAK;YACL;QACF;IACF;IAEA,qBAAqB;IACrB,MAAM,cAAc,IAAiB,EAA8C;QACjF,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,QAAQ;YACR,KAAK;YACL;QACF;IACF;IAEA,MAAM,YAAY,MAMjB,EAAoD;QACnD,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,QAAQ;YACR,KAAK;YACL;QACF;IACF;IAEA,MAAM,WAAW,EAAU,EAA8C;QACvE,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,QAAQ;YACR,KAAK,AAAC,aAAe,OAAH;QACpB;IACF;IAEA,MAAM,qBAAqB,EAAU,EAAE,OAAY,EAAwB;QACzE,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,QAAQ;YACR,KAAK,AAAC,aAAe,OAAH,IAAG;YACrB,MAAM;QACR;IACF;IAEA,MAAM,oBAA+C;QACnD,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,QAAQ;YACR,KAAK;QACP;IACF;IAEA,MAAM,qBAAqB,MAAiD,EAA6B;QACvG,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,QAAQ;YACR,KAAK;YACL;QACF;IACF;IAEA,mBAAmB;IACnB,MAAM,kBAAkB,IAA2C,EAA6C;QAC9G,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,QAAQ;YACR,KAAK;YACL;QACF;IACF;IAEA,MAAM,mBAAmB,OAAe,EAAwB;QAC9D,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,QAAQ;YACR,KAAK,AAAC,kCAAyC,OAAR;QACzC;IACF;IAEA,iBAAiB;IACjB,MAAM,iBAA4D;QAChE,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,QAAQ;YACR,KAAK;QACP;IACF;IAEA,MAAM,cAAc,MAAyB,EAAkD;QAC7F,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,QAAQ;YACR,KAAK;YACL;QACF;IACF;IAEA,MAAM,gBAAgB,MAAyB,EAAoD;QACjG,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,QAAQ;YACR,KAAK;YACL;QACF;IACF;IAEA,MAAM,kBAAkB,MAAiD,EAA6B;QACpG,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,QAAQ;YACR,KAAK;YACL;QACF;IACF;IAEA,MAAM,2BAA2B,QAAa,EAAwB;QACpE,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,QAAQ;YACR,KAAK;YACL,MAAM;QACR;IACF;IAEA,MAAM,aAAa,IAAU,EAA4C;QACvE,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,UAAU;QAE1B,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,QAAQ;YACR,KAAK;YACL,MAAM;YACN,SAAS;gBACP,gBAAgB;YAClB;QACF;IACF;IAEA,MAAM,eAAqC;QACzC,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,QAAQ;YACR,KAAK;QACP;IACF;IA3VA,aAAc;QAHd,+KAAQ,UAAR,KAAA;QACA,+KAAQ,WAAR,KAAA;QAGE,IAAI,CAAC,OAAO,GAAG,iEAAmC;QAElD,IAAI,CAAC,MAAM,GAAG,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;YACzB,SAAS,IAAI,CAAC,OAAO;YACrB,SAAS;YACT,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,IAAI,CAAC,iBAAiB;IACxB;AAgVF;AAEA,yCAAyC;AACzC,MAAM,YAAY,IAAI;uCACP", "debugId": null}}, {"offset": {"line": 327, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/trendz/frontend/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';\nimport { useRouter } from 'next/navigation';\nimport Cookies from 'js-cookie';\nimport toast from 'react-hot-toast';\nimport apiClient from '@/lib/api';\nimport type { User, LoginForm, RegisterForm, AuthContextType } from '@/types';\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\ninterface AuthProviderProps {\n  children: ReactNode;\n}\n\nexport const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {\n  const [user, setUser] = useState<User | null>(null);\n  const [token, setToken] = useState<string | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const router = useRouter();\n\n  // Initialize auth state\n  useEffect(() => {\n    const initAuth = async () => {\n      const savedToken = Cookies.get('token');\n      \n      if (savedToken) {\n        setToken(savedToken);\n        try {\n          const response = await apiClient.getMe();\n          if (response.success && response.data) {\n            setUser(response.data.user);\n          } else {\n            // Invalid token, clear it\n            Cookies.remove('token');\n            setToken(null);\n          }\n        } catch (error) {\n          console.error('Failed to get user info:', error);\n          Cookies.remove('token');\n          setToken(null);\n        }\n      }\n      \n      setIsLoading(false);\n    };\n\n    initAuth();\n  }, []);\n\n  const login = async (credentials: LoginForm) => {\n    try {\n      setIsLoading(true);\n      const response = await apiClient.login(credentials);\n      \n      if (response.success && response.data) {\n        const { token: newToken, user: userData } = response.data;\n        \n        // Save token to cookies\n        Cookies.set('token', newToken, { \n          expires: 7, // 7 days\n          secure: process.env.NODE_ENV === 'production',\n          sameSite: 'strict'\n        });\n        \n        setToken(newToken);\n        setUser(userData);\n        \n        toast.success('Login successful!');\n        \n        // Redirect based on user role\n        if (userData.role === 'admin') {\n          router.push('/admin');\n        } else {\n          router.push('/dashboard');\n        }\n      } else {\n        throw new Error(response.error || 'Login failed');\n      }\n    } catch (error: any) {\n      console.error('Login error:', error);\n      toast.error(error.message || 'Login failed');\n      throw error;\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const register = async (data: RegisterForm) => {\n    try {\n      setIsLoading(true);\n      const response = await apiClient.register(data);\n      \n      if (response.success && response.data) {\n        const { token: newToken, user: userData } = response.data;\n        \n        // Save token to cookies\n        Cookies.set('token', newToken, { \n          expires: 7, // 7 days\n          secure: process.env.NODE_ENV === 'production',\n          sameSite: 'strict'\n        });\n        \n        setToken(newToken);\n        setUser(userData);\n        \n        toast.success('Registration successful!');\n        router.push('/dashboard');\n      } else {\n        throw new Error(response.error || 'Registration failed');\n      }\n    } catch (error: any) {\n      console.error('Registration error:', error);\n      toast.error(error.message || 'Registration failed');\n      throw error;\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const logout = async () => {\n    try {\n      // Call logout endpoint\n      await apiClient.logout();\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      // Clear local state regardless of API call result\n      Cookies.remove('token');\n      setToken(null);\n      setUser(null);\n      toast.success('Logged out successfully');\n      router.push('/');\n    }\n  };\n\n  const updateProfile = async (data: Partial<User>) => {\n    try {\n      const response = await apiClient.updateProfile(data);\n      \n      if (response.success && response.data) {\n        setUser(response.data.user);\n        toast.success('Profile updated successfully');\n      } else {\n        throw new Error(response.error || 'Profile update failed');\n      }\n    } catch (error: any) {\n      console.error('Profile update error:', error);\n      toast.error(error.message || 'Profile update failed');\n      throw error;\n    }\n  };\n\n  const value: AuthContextType = {\n    user,\n    token,\n    isLoading,\n    isAuthenticated: !!user && !!token,\n    login,\n    register,\n    logout,\n    updateProfile,\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n\nexport const useAuth = (): AuthContextType => {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport default AuthContext;\n"], "names": [], "mappings": ";;;;;AA6DkB;;AA3DlB;AACA;AACA;AACA;AACA;;;AANA;;;;;;AASA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAMxD,MAAM,eAA4C;QAAC,EAAE,QAAQ,EAAE;;IACpE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,wBAAwB;IACxB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM;mDAAW;oBACf,MAAM,aAAa,wJAAA,CAAA,UAAO,CAAC,GAAG,CAAC;oBAE/B,IAAI,YAAY;wBACd,SAAS;wBACT,IAAI;4BACF,MAAM,WAAW,MAAM,oHAAA,CAAA,UAAS,CAAC,KAAK;4BACtC,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gCACrC,QAAQ,SAAS,IAAI,CAAC,IAAI;4BAC5B,OAAO;gCACL,0BAA0B;gCAC1B,wJAAA,CAAA,UAAO,CAAC,MAAM,CAAC;gCACf,SAAS;4BACX;wBACF,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,4BAA4B;4BAC1C,wJAAA,CAAA,UAAO,CAAC,MAAM,CAAC;4BACf,SAAS;wBACX;oBACF;oBAEA,aAAa;gBACf;;YAEA;QACF;iCAAG,EAAE;IAEL,MAAM,QAAQ,OAAO;QACnB,IAAI;YACF,aAAa;YACb,MAAM,WAAW,MAAM,oHAAA,CAAA,UAAS,CAAC,KAAK,CAAC;YAEvC,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,MAAM,EAAE,OAAO,QAAQ,EAAE,MAAM,QAAQ,EAAE,GAAG,SAAS,IAAI;gBAEzD,wBAAwB;gBACxB,wJAAA,CAAA,UAAO,CAAC,GAAG,CAAC,SAAS,UAAU;oBAC7B,SAAS;oBACT,QAAQ,oDAAyB;oBACjC,UAAU;gBACZ;gBAEA,SAAS;gBACT,QAAQ;gBAER,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;gBAEd,8BAA8B;gBAC9B,IAAI,SAAS,IAAI,KAAK,SAAS;oBAC7B,OAAO,IAAI,CAAC;gBACd,OAAO;oBACL,OAAO,IAAI,CAAC;gBACd;YACF,OAAO;gBACL,MAAM,IAAI,MAAM,SAAS,KAAK,IAAI;YACpC;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,gBAAgB;YAC9B,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;YAC7B,MAAM;QACR,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,WAAW,OAAO;QACtB,IAAI;YACF,aAAa;YACb,MAAM,WAAW,MAAM,oHAAA,CAAA,UAAS,CAAC,QAAQ,CAAC;YAE1C,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,MAAM,EAAE,OAAO,QAAQ,EAAE,MAAM,QAAQ,EAAE,GAAG,SAAS,IAAI;gBAEzD,wBAAwB;gBACxB,wJAAA,CAAA,UAAO,CAAC,GAAG,CAAC,SAAS,UAAU;oBAC7B,SAAS;oBACT,QAAQ,oDAAyB;oBACjC,UAAU;gBACZ;gBAEA,SAAS;gBACT,QAAQ;gBAER,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;gBACd,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,MAAM,IAAI,MAAM,SAAS,KAAK,IAAI;YACpC;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,uBAAuB;YACrC,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;YAC7B,MAAM;QACR,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,SAAS;QACb,IAAI;YACF,uBAAuB;YACvB,MAAM,oHAAA,CAAA,UAAS,CAAC,MAAM;QACxB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC,SAAU;YACR,kDAAkD;YAClD,wJAAA,CAAA,UAAO,CAAC,MAAM,CAAC;YACf,SAAS;YACT,QAAQ;YACR,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;YACd,OAAO,IAAI,CAAC;QACd;IACF;IAEA,MAAM,gBAAgB,OAAO;QAC3B,IAAI;YACF,MAAM,WAAW,MAAM,oHAAA,CAAA,UAAS,CAAC,aAAa,CAAC;YAE/C,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,QAAQ,SAAS,IAAI,CAAC,IAAI;gBAC1B,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;YAChB,OAAO;gBACL,MAAM,IAAI,MAAM,SAAS,KAAK,IAAI;YACpC;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,yBAAyB;YACvC,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;YAC7B,MAAM;QACR;IACF;IAEA,MAAM,QAAyB;QAC7B;QACA;QACA;QACA,iBAAiB,CAAC,CAAC,QAAQ,CAAC,CAAC;QAC7B;QACA;QACA;QACA;IACF;IAEA,qBACE,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;GA1Ja;;QAII,qIAAA,CAAA,YAAS;;;KAJb;AA4JN,MAAM,UAAU;;IACrB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANa;uCAQE", "debugId": null}}]}