{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/trendz/frontend/src/app/auth/register/page.tsx"], "sourcesContent": ["export default function Register() {\n  return (\n    <div className=\"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8\">\n      <div className=\"sm:mx-auto sm:w-full sm:max-w-md\">\n        <div className=\"flex justify-center\">\n          <div className=\"w-12 h-12 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg flex items-center justify-center\">\n            <span className=\"text-white font-bold text-xl\">T</span>\n          </div>\n        </div>\n        <h2 className=\"mt-6 text-center text-3xl font-extrabold text-gray-900\">\n          Create your account\n        </h2>\n        <p className=\"mt-2 text-center text-sm text-gray-600\">\n          Or{' '}\n          <a href=\"/auth/login\" className=\"font-medium text-purple-600 hover:text-purple-500\">\n            sign in to your existing account\n          </a>\n        </p>\n      </div>\n\n      <div className=\"mt-8 sm:mx-auto sm:w-full sm:max-w-md\">\n        <div className=\"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10\">\n          <form className=\"space-y-6\">\n            <div className=\"grid grid-cols-2 gap-4\">\n              <div>\n                <label htmlFor=\"firstName\" className=\"block text-sm font-medium text-gray-700\">\n                  First name\n                </label>\n                <div className=\"mt-1\">\n                  <input\n                    id=\"firstName\"\n                    name=\"firstName\"\n                    type=\"text\"\n                    required\n                    className=\"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm\"\n                    placeholder=\"John\"\n                  />\n                </div>\n              </div>\n\n              <div>\n                <label htmlFor=\"lastName\" className=\"block text-sm font-medium text-gray-700\">\n                  Last name\n                </label>\n                <div className=\"mt-1\">\n                  <input\n                    id=\"lastName\"\n                    name=\"lastName\"\n                    type=\"text\"\n                    required\n                    className=\"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm\"\n                    placeholder=\"Doe\"\n                  />\n                </div>\n              </div>\n            </div>\n\n            <div>\n              <label htmlFor=\"username\" className=\"block text-sm font-medium text-gray-700\">\n                Username\n              </label>\n              <div className=\"mt-1\">\n                <input\n                  id=\"username\"\n                  name=\"username\"\n                  type=\"text\"\n                  required\n                  className=\"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm\"\n                  placeholder=\"johndoe\"\n                />\n              </div>\n            </div>\n\n            <div>\n              <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700\">\n                Email address\n              </label>\n              <div className=\"mt-1\">\n                <input\n                  id=\"email\"\n                  name=\"email\"\n                  type=\"email\"\n                  autoComplete=\"email\"\n                  required\n                  className=\"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm\"\n                  placeholder=\"<EMAIL>\"\n                />\n              </div>\n            </div>\n\n            <div>\n              <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700\">\n                Password\n              </label>\n              <div className=\"mt-1\">\n                <input\n                  id=\"password\"\n                  name=\"password\"\n                  type=\"password\"\n                  autoComplete=\"new-password\"\n                  required\n                  className=\"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm\"\n                  placeholder=\"\"\n                />\n              </div>\n            </div>\n\n            <div>\n              <label htmlFor=\"confirmPassword\" className=\"block text-sm font-medium text-gray-700\">\n                Confirm Password\n              </label>\n              <div className=\"mt-1\">\n                <input\n                  id=\"confirmPassword\"\n                  name=\"confirmPassword\"\n                  type=\"password\"\n                  autoComplete=\"new-password\"\n                  required\n                  className=\"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm\"\n                  placeholder=\"\"\n                />\n              </div>\n            </div>\n\n            <div>\n              <label htmlFor=\"phone\" className=\"block text-sm font-medium text-gray-700\">\n                Phone number (optional)\n              </label>\n              <div className=\"mt-1\">\n                <input\n                  id=\"phone\"\n                  name=\"phone\"\n                  type=\"tel\"\n                  className=\"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm\"\n                  placeholder=\"+****************\"\n                />\n              </div>\n            </div>\n\n            <div className=\"flex items-center\">\n              <input\n                id=\"terms\"\n                name=\"terms\"\n                type=\"checkbox\"\n                required\n                className=\"h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded\"\n              />\n              <label htmlFor=\"terms\" className=\"ml-2 block text-sm text-gray-900\">\n                I agree to the{' '}\n                <a href=\"/terms\" className=\"text-purple-600 hover:text-purple-500\">\n                  Terms of Service\n                </a>{' '}\n                and{' '}\n                <a href=\"/privacy\" className=\"text-purple-600 hover:text-purple-500\">\n                  Privacy Policy\n                </a>\n              </label>\n            </div>\n\n            <div>\n              <button\n                type=\"submit\"\n                className=\"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500\"\n              >\n                Create Account\n              </button>\n            </div>\n          </form>\n\n          <div className=\"mt-6\">\n            <div className=\"relative\">\n              <div className=\"absolute inset-0 flex items-center\">\n                <div className=\"w-full border-t border-gray-300\" />\n              </div>\n              <div className=\"relative flex justify-center text-sm\">\n                <span className=\"px-2 bg-white text-gray-500\">Why choose Trendz?</span>\n              </div>\n            </div>\n\n            <div className=\"mt-6 grid grid-cols-2 gap-4 text-center\">\n              <div className=\"text-sm text-gray-600\">\n                <div className=\"font-medium\">Secure & Fast</div>\n                <div>Enterprise-grade security</div>\n              </div>\n              <div className=\"text-sm text-gray-600\">\n                <div className=\"font-medium\">24/7 Support</div>\n                <div>Round-the-clock assistance</div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\r\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,WAAU;0CAA+B;;;;;;;;;;;;;;;;kCAGnD,8OAAC;wBAAG,WAAU;kCAAyD;;;;;;kCAGvE,8OAAC;wBAAE,WAAU;;4BAAyC;4BACjD;0CACH,8OAAC;gCAAE,MAAK;gCAAc,WAAU;0CAAoD;;;;;;;;;;;;;;;;;;0BAMxF,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAK,WAAU;;8CACd,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAM,SAAQ;oDAAY,WAAU;8DAA0C;;;;;;8DAG/E,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDACC,IAAG;wDACH,MAAK;wDACL,MAAK;wDACL,QAAQ;wDACR,WAAU;wDACV,aAAY;;;;;;;;;;;;;;;;;sDAKlB,8OAAC;;8DACC,8OAAC;oDAAM,SAAQ;oDAAW,WAAU;8DAA0C;;;;;;8DAG9E,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDACC,IAAG;wDACH,MAAK;wDACL,MAAK;wDACL,QAAQ;wDACR,WAAU;wDACV,aAAY;;;;;;;;;;;;;;;;;;;;;;;8CAMpB,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAA0C;;;;;;sDAG9E,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDACC,IAAG;gDACH,MAAK;gDACL,MAAK;gDACL,QAAQ;gDACR,WAAU;gDACV,aAAY;;;;;;;;;;;;;;;;;8CAKlB,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAQ,WAAU;sDAA0C;;;;;;sDAG3E,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDACC,IAAG;gDACH,MAAK;gDACL,MAAK;gDACL,cAAa;gDACb,QAAQ;gDACR,WAAU;gDACV,aAAY;;;;;;;;;;;;;;;;;8CAKlB,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAA0C;;;;;;sDAG9E,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDACC,IAAG;gDACH,MAAK;gDACL,MAAK;gDACL,cAAa;gDACb,QAAQ;gDACR,WAAU;gDACV,aAAY;;;;;;;;;;;;;;;;;8CAKlB,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAkB,WAAU;sDAA0C;;;;;;sDAGrF,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDACC,IAAG;gDACH,MAAK;gDACL,MAAK;gDACL,cAAa;gDACb,QAAQ;gDACR,WAAU;gDACV,aAAY;;;;;;;;;;;;;;;;;8CAKlB,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAQ,WAAU;sDAA0C;;;;;;sDAG3E,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDACC,IAAG;gDACH,MAAK;gDACL,MAAK;gDACL,WAAU;gDACV,aAAY;;;;;;;;;;;;;;;;;8CAKlB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,IAAG;4CACH,MAAK;4CACL,MAAK;4CACL,QAAQ;4CACR,WAAU;;;;;;sDAEZ,8OAAC;4CAAM,SAAQ;4CAAQ,WAAU;;gDAAmC;gDACnD;8DACf,8OAAC;oDAAE,MAAK;oDAAS,WAAU;8DAAwC;;;;;;gDAE9D;gDAAI;gDACL;8DACJ,8OAAC;oDAAE,MAAK;oDAAW,WAAU;8DAAwC;;;;;;;;;;;;;;;;;;8CAMzE,8OAAC;8CACC,cAAA,8OAAC;wCACC,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;sCAML,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;;;;;;;;;;sDAEjB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAA8B;;;;;;;;;;;;;;;;;8CAIlD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAc;;;;;;8DAC7B,8OAAC;8DAAI;;;;;;;;;;;;sDAEP,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAc;;;;;;8DAC7B,8OAAC;8DAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrB", "debugId": null}}]}