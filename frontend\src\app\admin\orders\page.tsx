'use client';

import { useState } from 'react';
import Layout from '../../../components/Layout';
import { useLanguage } from '../../../contexts/LanguageContext';

export default function AdminOrders() {
  const { t } = useLanguage();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [sortBy, setSortBy] = useState('date');

  const orders = [
    {
      id: 'TRZ001',
      user: '<PERSON>',
      userEmail: '<EMAIL>',
      service: 'Instagram Followers',
      platform: 'Instagram',
      quantity: 1000,
      delivered: 1000,
      price: 12.50,
      status: 'completed',
      date: '2025-01-30',
      link: 'https://instagram.com/username',
      startCount: 1250,
      remains: 0,
      speed: 'Fast',
      provider: 'Provider A'
    },
    {
      id: 'TRZ002',
      user: '<PERSON>',
      userEmail: '<EMAIL>',
      service: 'TikTok Views',
      platform: 'TikTok',
      quantity: 5000,
      delivered: 3200,
      price: 8.75,
      status: 'in_progress',
      date: '2025-01-29',
      link: 'https://tiktok.com/@username/video/123',
      startCount: 850,
      remains: 1800,
      speed: 'Instant',
      provider: 'Provider B'
    },
    {
      id: 'TRZ003',
      user: 'Mohamed Hassan',
      userEmail: '<EMAIL>',
      service: 'YouTube Subscribers',
      platform: 'YouTube',
      quantity: 500,
      delivered: 0,
      price: 25.00,
      status: 'pending',
      date: '2025-01-28',
      link: 'https://youtube.com/channel/UC123',
      startCount: 1200,
      remains: 500,
      speed: 'Medium',
      provider: 'Provider C'
    },
    {
      id: 'TRZ004',
      user: 'Fatima Omar',
      userEmail: '<EMAIL>',
      service: 'Facebook Likes',
      platform: 'Facebook',
      quantity: 2000,
      delivered: 1500,
      price: 15.30,
      status: 'partial',
      date: '2025-01-27',
      link: 'https://facebook.com/post/123',
      startCount: 320,
      remains: 500,
      speed: 'Fast',
      provider: 'Provider A'
    },
    {
      id: 'TRZ005',
      user: 'Khaled Ibrahim',
      userEmail: '<EMAIL>',
      service: 'Twitter Followers',
      platform: 'Twitter',
      quantity: 800,
      delivered: 0,
      price: 18.40,
      status: 'cancelled',
      date: '2025-01-26',
      link: 'https://twitter.com/username',
      startCount: 450,
      remains: 800,
      speed: 'Medium',
      provider: 'Provider B'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'in_progress': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'pending': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'partial': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300';
      case 'cancelled': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      case 'refunded': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
  };

  const getStatusText = (status: string) => {
    return t(`orders.${status}`) || status;
  };

  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case 'Instagram': return '📷';
      case 'TikTok': return '🎵';
      case 'YouTube': return '📺';
      case 'Facebook': return '📘';
      case 'Twitter': return '🐦';
      case 'Snapchat': return '👻';
      default: return '📱';
    }
  };

  const filteredOrders = orders.filter(order => {
    const matchesFilter = statusFilter === 'all' || order.status === statusFilter;
    const matchesSearch = order.service.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         order.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         order.user.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesFilter && matchesSearch;
  });

  const getProgress = (delivered: number, quantity: number) => {
    return Math.round((delivered / quantity) * 100);
  };

  const handleOrderAction = (orderId: string, action: string) => {
    console.log(`Action ${action} for order ${orderId}`);
  };

  return (
    <Layout currentPage="orders" isAdmin={true}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">{t('admin.orderManagement')}</h1>
          <p className="text-gray-600 dark:text-gray-300">Monitor and manage all platform orders</p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                <svg className="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{t('admin.totalOrders')}</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{orders.length}</p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg">
                <svg className="w-6 h-6 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{t('admin.pendingOrders')}</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {orders.filter(o => o.status === 'pending').length}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                <svg className="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Completed</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {orders.filter(o => o.status === 'completed').length}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 dark:bg-purple-900 rounded-lg">
                <svg className="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Revenue</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  ${orders.reduce((sum, order) => sum + order.price, 0).toFixed(2)}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Filters and Search */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow mb-6">
          <div className="p-6">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
              {/* Search */}
              <div className="relative flex-1 max-w-md">
                <input
                  type="text"
                  placeholder={t('common.search') + ' orders...'}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 pr-4 py-2 w-full border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-purple-500 focus:border-purple-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
                <svg className="w-5 h-5 text-gray-400 absolute left-3 top-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>

              {/* Filters */}
              <div className="flex space-x-4 rtl:space-x-reverse">
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 focus:outline-none focus:ring-purple-500 focus:border-purple-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                >
                  <option value="all">All Status</option>
                  <option value="pending">Pending</option>
                  <option value="in_progress">In Progress</option>
                  <option value="completed">Completed</option>
                  <option value="partial">Partial</option>
                  <option value="cancelled">Cancelled</option>
                </select>

                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 focus:outline-none focus:ring-purple-500 focus:border-purple-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                >
                  <option value="date">Date</option>
                  <option value="price">Price</option>
                  <option value="status">Status</option>
                  <option value="user">User</option>
                </select>

                <button className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors">
                  Export
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Orders List */}
        <div className="space-y-4">
          {filteredOrders.length === 0 ? (
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-8 text-center">
              <svg className="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
              </svg>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No orders found</h3>
              <p className="text-gray-600 dark:text-gray-300">No orders match your search criteria.</p>
            </div>
          ) : (
            filteredOrders.map((order) => (
              <div key={order.id} className="bg-white dark:bg-gray-800 rounded-lg shadow hover:shadow-md transition-shadow">
                <div className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-4 rtl:space-x-reverse">
                      <div className="text-3xl">{getPlatformIcon(order.platform)}</div>
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 rtl:space-x-reverse mb-2">
                          <h3 className="text-lg font-medium text-gray-900 dark:text-white">{order.service}</h3>
                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(order.status)}`}>
                            {getStatusText(order.status)}
                          </span>
                        </div>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">Order #{order.id}</p>
                        <p className="text-sm text-gray-500 dark:text-gray-500">User: {order.user} ({order.userEmail})</p>
                        <p className="text-sm text-gray-500 dark:text-gray-500">Date: {order.date}</p>
                        <p className="text-sm text-gray-500 dark:text-gray-500 truncate">Link: {order.link}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-lg font-bold text-gray-900 dark:text-white">${order.price.toFixed(2)}</p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">{order.quantity.toLocaleString()} items</p>
                      <p className="text-sm text-gray-500 dark:text-gray-500">Provider: {order.provider}</p>
                    </div>
                  </div>

                  {/* Progress Bar */}
                  {(order.status === 'in_progress' || order.status === 'partial') && (
                    <div className="mt-4">
                      <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-1">
                        <span>Progress: {order.delivered.toLocaleString()} / {order.quantity.toLocaleString()}</span>
                        <span>{getProgress(order.delivered, order.quantity)}%</span>
                      </div>
                      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <div 
                          className="bg-purple-600 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${getProgress(order.delivered, order.quantity)}%` }}
                        ></div>
                      </div>
                    </div>
                  )}

                  {/* Order Details */}
                  <div className="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <p className="text-gray-600 dark:text-gray-400">Start Count</p>
                      <p className="font-medium text-gray-900 dark:text-white">{order.startCount.toLocaleString()}</p>
                    </div>
                    <div>
                      <p className="text-gray-600 dark:text-gray-400">Delivered</p>
                      <p className="font-medium text-gray-900 dark:text-white">{order.delivered.toLocaleString()}</p>
                    </div>
                    <div>
                      <p className="text-gray-600 dark:text-gray-400">Remains</p>
                      <p className="font-medium text-gray-900 dark:text-white">{order.remains.toLocaleString()}</p>
                    </div>
                    <div>
                      <p className="text-gray-600 dark:text-gray-400">Speed</p>
                      <p className="font-medium text-gray-900 dark:text-white">{order.speed}</p>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="mt-4 flex flex-wrap gap-2">
                    <button
                      onClick={() => handleOrderAction(order.id, 'view')}
                      className="text-purple-600 hover:text-purple-700 dark:text-purple-400 dark:hover:text-purple-300 text-sm font-medium"
                    >
                      {t('common.view')} Details
                    </button>
                    {order.status === 'pending' && (
                      <>
                        <button
                          onClick={() => handleOrderAction(order.id, 'start')}
                          className="text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300 text-sm font-medium"
                        >
                          Start Order
                        </button>
                        <button
                          onClick={() => handleOrderAction(order.id, 'cancel')}
                          className="text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 text-sm font-medium"
                        >
                          {t('orders.cancelOrder')}
                        </button>
                      </>
                    )}
                    {order.status === 'in_progress' && (
                      <button
                        onClick={() => handleOrderAction(order.id, 'pause')}
                        className="text-yellow-600 hover:text-yellow-700 dark:text-yellow-400 dark:hover:text-yellow-300 text-sm font-medium"
                      >
                        Pause Order
                      </button>
                    )}
                    {(order.status === 'completed' || order.status === 'partial') && (
                      <button
                        onClick={() => handleOrderAction(order.id, 'refill')}
                        className="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 text-sm font-medium"
                      >
                        {t('orders.requestRefill')}
                      </button>
                    )}
                  </div>
                </div>
              </div>
            ))
          )}
        </div>

        {/* Pagination */}
        {filteredOrders.length > 0 && (
          <div className="mt-8 flex justify-center">
            <nav className="flex items-center space-x-2 rtl:space-x-reverse">
              <button className="px-3 py-2 text-sm font-medium text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700">
                {t('common.previous')}
              </button>
              <button className="px-3 py-2 text-sm font-medium text-white bg-purple-600 border border-purple-600 rounded-md">
                1
              </button>
              <button className="px-3 py-2 text-sm font-medium text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700">
                2
              </button>
              <button className="px-3 py-2 text-sm font-medium text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700">
                {t('common.next')}
              </button>
            </nav>
          </div>
        )}
      </div>
    </Layout>
  );
}
