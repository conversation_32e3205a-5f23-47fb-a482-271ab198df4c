'use client';

import { ReactNode } from 'react';
import Header from './Header';
import Footer from './Footer';
import { useLanguage } from '../contexts/LanguageContext';

interface LayoutProps {
  children: ReactNode;
  currentPage?: string;
  isAdmin?: boolean;
  user?: {
    name: string;
    balance?: number;
  } | null;
  showFooter?: boolean;
}

export default function Layout({ 
  children, 
  currentPage, 
  isAdmin = false, 
  user = null, 
  showFooter = true 
}: LayoutProps) {
  const { isRTL } = useLanguage();

  return (
    <div className={`min-h-screen bg-gray-50 dark:bg-gray-900 ${isRTL ? 'rtl' : 'ltr'}`}>
      <Header currentPage={currentPage} isAdmin={isAdmin} user={user} />
      <main className="flex-1">
        {children}
      </main>
      {showFooter && <Footer />}
    </div>
  );
}
