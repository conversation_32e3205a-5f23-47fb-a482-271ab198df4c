'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useParams } from 'next/navigation';

export default function ServiceDetails() {
  const params = useParams();
  const [quantity, setQuantity] = useState('1000');
  const [link, setLink] = useState('');
  const [customComments, setCustomComments] = useState(['']);
  const [showOrderForm, setShowOrderForm] = useState(false);

  // Mock service data - في التطبيق الحقيقي سيتم جلبها من API
  const service = {
    id: params.id,
    name: 'Instagram Followers - High Quality',
    platform: 'Instagram',
    category: 'Followers',
    price: 2.50,
    priceUnit: 'per_1000',
    minOrder: 100,
    maxOrder: 50000,
    description: 'Get high-quality Instagram followers from real and active accounts. These followers will help boost your social proof and engagement rates.',
    features: [
      'Real and active accounts',
      'High retention rate',
      'Gradual delivery',
      'No password required',
      'Lifetime guarantee'
    ],
    requirements: {
      linkRequired: true,
      usernameRequired: false,
      customCommentsRequired: false
    },
    averageTime: '1-6 hours',
    speed: 'Fast',
    quality: 'High Quality',
    refillEnabled: true,
    refillPeriod: 30,
    cancelEnabled: true,
    successRate: 98.5,
    totalOrders: 15420,
    icon: '📷'
  };

  const calculatePrice = (qty: number) => {
    const units = Math.ceil(qty / 1000);
    return (units * service.price).toFixed(2);
  };

  const handleQuantityChange = (value: string) => {
    const numValue = parseInt(value) || 0;
    if (numValue >= service.minOrder && numValue <= service.maxOrder) {
      setQuantity(value);
    }
  };

  const handleAddComment = () => {
    if (customComments.length < 10) {
      setCustomComments([...customComments, '']);
    }
  };

  const handleRemoveComment = (index: number) => {
    if (customComments.length > 1) {
      setCustomComments(customComments.filter((_, i) => i !== index));
    }
  };

  const handleCommentChange = (index: number, value: string) => {
    const newComments = [...customComments];
    newComments[index] = value;
    setCustomComments(newComments);
  };

  const handleOrderSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Order submitted:', {
      serviceId: service.id,
      quantity: parseInt(quantity),
      link,
      customComments: customComments.filter(comment => comment.trim() !== '')
    });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <Link href="/" className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-sm">T</span>
                </div>
                <span className="text-xl font-bold text-gray-900">Trendz</span>
              </Link>
            </div>
            
            <nav className="hidden md:flex space-x-8">
              <Link href="/dashboard" className="text-gray-600 hover:text-purple-600">Dashboard</Link>
              <Link href="/services" className="text-purple-600 font-medium">Services</Link>
              <Link href="/orders" className="text-gray-600 hover:text-purple-600">Orders</Link>
              <Link href="/payments" className="text-gray-600 hover:text-purple-600">Payments</Link>
              <Link href="/profile" className="text-gray-600 hover:text-purple-600">Profile</Link>
            </nav>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Breadcrumb */}
        <nav className="flex mb-8" aria-label="Breadcrumb">
          <ol className="flex items-center space-x-4">
            <li>
              <Link href="/services" className="text-gray-500 hover:text-gray-700">Services</Link>
            </li>
            <li>
              <svg className="flex-shrink-0 h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
              </svg>
            </li>
            <li>
              <span className="text-gray-900 font-medium">{service.name}</span>
            </li>
          </ol>
        </nav>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Service Details */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow">
              <div className="p-6">
                <div className="flex items-start space-x-4 mb-6">
                  <div className="text-4xl">{service.icon}</div>
                  <div className="flex-1">
                    <h1 className="text-2xl font-bold text-gray-900 mb-2">{service.name}</h1>
                    <div className="flex items-center space-x-4 text-sm text-gray-600 mb-4">
                      <span className="bg-purple-100 text-purple-800 px-2 py-1 rounded-full">{service.platform}</span>
                      <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full">{service.category}</span>
                      <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full">{service.quality}</span>
                    </div>
                    <p className="text-gray-700 leading-relaxed">{service.description}</p>
                  </div>
                </div>

                {/* Service Stats */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                  <div className="text-center p-4 bg-gray-50 rounded-lg">
                    <p className="text-2xl font-bold text-purple-600">${service.price}</p>
                    <p className="text-sm text-gray-600">per 1000</p>
                  </div>
                  <div className="text-center p-4 bg-gray-50 rounded-lg">
                    <p className="text-2xl font-bold text-green-600">{service.successRate}%</p>
                    <p className="text-sm text-gray-600">Success Rate</p>
                  </div>
                  <div className="text-center p-4 bg-gray-50 rounded-lg">
                    <p className="text-2xl font-bold text-blue-600">{service.totalOrders.toLocaleString()}</p>
                    <p className="text-sm text-gray-600">Total Orders</p>
                  </div>
                  <div className="text-center p-4 bg-gray-50 rounded-lg">
                    <p className="text-2xl font-bold text-orange-600">{service.averageTime}</p>
                    <p className="text-sm text-gray-600">Avg. Time</p>
                  </div>
                </div>

                {/* Features */}
                <div className="mb-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-3">Features</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                    {service.features.map((feature, index) => (
                      <div key={index} className="flex items-center space-x-2">
                        <svg className="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                        <span className="text-sm text-gray-700">{feature}</span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Service Details */}
                <div className="border-t pt-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-3">Service Details</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Min Order:</span>
                      <span className="font-medium">{service.minOrder.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Max Order:</span>
                      <span className="font-medium">{service.maxOrder.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Speed:</span>
                      <span className="font-medium">{service.speed}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Refill:</span>
                      <span className="font-medium">{service.refillEnabled ? `${service.refillPeriod} days` : 'No'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Cancel:</span>
                      <span className="font-medium">{service.cancelEnabled ? 'Yes' : 'No'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Link Required:</span>
                      <span className="font-medium">{service.requirements.linkRequired ? 'Yes' : 'No'}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Order Form */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow sticky top-8">
              <div className="p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Place Order</h3>
                
                <form onSubmit={handleOrderSubmit} className="space-y-4">
                  {/* Quantity */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Quantity</label>
                    <input
                      type="number"
                      value={quantity}
                      onChange={(e) => handleQuantityChange(e.target.value)}
                      min={service.minOrder}
                      max={service.maxOrder}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-purple-500 focus:border-purple-500"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Min: {service.minOrder.toLocaleString()} - Max: {service.maxOrder.toLocaleString()}
                    </p>
                  </div>

                  {/* Link */}
                  {service.requirements.linkRequired && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Link</label>
                      <input
                        type="url"
                        value={link}
                        onChange={(e) => setLink(e.target.value)}
                        placeholder="https://instagram.com/username"
                        className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-purple-500 focus:border-purple-500"
                        required
                      />
                    </div>
                  )}

                  {/* Custom Comments */}
                  {service.requirements.customCommentsRequired && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Custom Comments</label>
                      {customComments.map((comment, index) => (
                        <div key={index} className="flex space-x-2 mb-2">
                          <input
                            type="text"
                            value={comment}
                            onChange={(e) => handleCommentChange(index, e.target.value)}
                            placeholder={`Comment ${index + 1}`}
                            className="flex-1 border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-purple-500 focus:border-purple-500"
                          />
                          {customComments.length > 1 && (
                            <button
                              type="button"
                              onClick={() => handleRemoveComment(index)}
                              className="px-2 py-2 text-red-600 hover:text-red-700"
                            >
                              ×
                            </button>
                          )}
                        </div>
                      ))}
                      {customComments.length < 10 && (
                        <button
                          type="button"
                          onClick={handleAddComment}
                          className="text-sm text-purple-600 hover:text-purple-700"
                        >
                          + Add Comment
                        </button>
                      )}
                    </div>
                  )}

                  {/* Price Calculation */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm text-gray-600">Quantity:</span>
                      <span className="font-medium">{parseInt(quantity || '0').toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm text-gray-600">Price per 1000:</span>
                      <span className="font-medium">${service.price}</span>
                    </div>
                    <div className="border-t pt-2">
                      <div className="flex justify-between items-center">
                        <span className="font-medium text-gray-900">Total:</span>
                        <span className="text-xl font-bold text-purple-600">
                          ${calculatePrice(parseInt(quantity || '0'))}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Submit Button */}
                  <button
                    type="submit"
                    className="w-full bg-purple-600 text-white py-3 px-4 rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 font-medium"
                  >
                    Place Order - ${calculatePrice(parseInt(quantity || '0'))}
                  </button>
                </form>

                {/* Additional Info */}
                <div className="mt-4 text-xs text-gray-500">
                  <p>• Orders are processed automatically</p>
                  <p>• You will receive email notifications</p>
                  <p>• Refill guarantee: {service.refillPeriod} days</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
