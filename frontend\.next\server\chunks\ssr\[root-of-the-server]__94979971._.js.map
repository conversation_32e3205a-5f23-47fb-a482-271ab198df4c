{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/trendz/frontend/src/app/page.tsx"], "sourcesContent": ["export default function Home() {\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-purple-600 via-blue-600 to-indigo-700 text-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20\">\n        <div className=\"text-center\">\n          <h1 className=\"text-4xl md:text-6xl font-bold mb-6\">\n            <span className=\"block\">Trendz SMM Panel</span>\n            <span className=\"block text-transparent bg-clip-text bg-gradient-to-r from-yellow-400 to-orange-500\">\n              Professional Services\n            </span>\n          </h1>\n          <p className=\"text-xl text-purple-100 mb-8 max-w-3xl mx-auto\">\n            Boost your social media presence with our premium services for all major platforms.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <a href=\"/services\" className=\"bg-white text-purple-600 px-8 py-4 rounded-lg font-semibold text-lg hover:bg-gray-100 transition-colors\">\n              Browse Services\n            </a>\n            <a href=\"/register\" className=\"border-2 border-white text-white px-8 py-4 rounded-lg font-semibold text-lg hover:bg-white hover:text-purple-600 transition-colors\">\n              Get Started Free\n            </a>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\r\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC;gCAAK,WAAU;0CAAQ;;;;;;0CACxB,8OAAC;gCAAK,WAAU;0CAAqF;;;;;;;;;;;;kCAIvG,8OAAC;wBAAE,WAAU;kCAAiD;;;;;;kCAG9D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,MAAK;gCAAY,WAAU;0CAA0G;;;;;;0CAGxI,8OAAC;gCAAE,MAAK;gCAAY,WAAU;0CAAqI;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ/K", "debugId": null}}]}