{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/trendz/frontend/src/app/page.tsx"], "sourcesContent": ["import Link from 'next/link';\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <div className=\"bg-white shadow\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center py-6\">\n            <div className=\"flex items-center\">\n              <Link href=\"/\" className=\"flex items-center space-x-2\">\n                <div className=\"w-8 h-8 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg flex items-center justify-center\">\n                  <span className=\"text-white font-bold text-sm\">T</span>\n                </div>\n                <span className=\"text-xl font-bold text-gray-900\">Trendz</span>\n              </Link>\n            </div>\n\n            <nav className=\"hidden md:flex space-x-8\">\n              <Link href=\"/services\" className=\"text-gray-600 hover:text-purple-600\">Services</Link>\n              <Link href=\"/faq\" className=\"text-gray-600 hover:text-purple-600\">FAQ</Link>\n              <Link href=\"/support\" className=\"text-gray-600 hover:text-purple-600\">Support</Link>\n              <Link href=\"/dashboard\" className=\"text-gray-600 hover:text-purple-600\">Dashboard</Link>\n            </nav>\n\n            <div className=\"flex items-center space-x-4\">\n              <Link href=\"/auth/login\" className=\"text-gray-600 hover:text-purple-600\">Login</Link>\n              <Link href=\"/auth/register\" className=\"bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 transition-colors\">\n                Sign Up\n              </Link>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Hero Section */}\n      <div className=\"bg-gradient-to-br from-purple-600 via-blue-600 to-indigo-700 text-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20\">\n          <div className=\"text-center\">\n            <h1 className=\"text-4xl md:text-6xl font-bold mb-6\">\n              <span className=\"block\">Trendz SMM Panel</span>\n              <span className=\"block text-transparent bg-clip-text bg-gradient-to-r from-yellow-400 to-orange-500\">\n                Professional Services\n              </span>\n            </h1>\n            <p className=\"text-xl text-purple-100 mb-8 max-w-3xl mx-auto\">\n              Boost your social media presence with our premium services for all major platforms. Fast, reliable, and affordable.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <Link href=\"/services\" className=\"bg-white text-purple-600 px-8 py-4 rounded-lg font-semibold text-lg hover:bg-gray-100 transition-colors\">\n                Browse Services\n              </Link>\n              <Link href=\"/auth/register\" className=\"border-2 border-white text-white px-8 py-4 rounded-lg font-semibold text-lg hover:bg-white hover:text-purple-600 transition-colors\">\n                Get Started Free\n              </Link>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Features Section */}\n      <div className=\"py-16 bg-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">Why Choose Trendz?</h2>\n            <p className=\"text-lg text-gray-600\">We provide the best social media marketing services with guaranteed results</p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            <div className=\"text-center p-6\">\n              <div className=\"w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <svg className=\"w-8 h-8 text-purple-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n                </svg>\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">Fast Delivery</h3>\n              <p className=\"text-gray-600\">Most orders start within 1-6 hours and complete quickly with our automated systems.</p>\n            </div>\n\n            <div className=\"text-center p-6\">\n              <div className=\"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <svg className=\"w-8 h-8 text-green-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                </svg>\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">High Quality</h3>\n              <p className=\"text-gray-600\">Real accounts and high-quality engagement that looks natural and authentic.</p>\n            </div>\n\n            <div className=\"text-center p-6\">\n              <div className=\"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <svg className=\"w-8 h-8 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 2.25a9.75 9.75 0 109.75 9.75A9.75 9.75 0 0012 2.25z\" />\n                </svg>\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">24/7 Support</h3>\n              <p className=\"text-gray-600\">Round-the-clock customer support to help you with any questions or issues.</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Platforms Section */}\n      <div className=\"py-16 bg-gray-50\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">Supported Platforms</h2>\n            <p className=\"text-lg text-gray-600\">We support all major social media platforms</p>\n          </div>\n\n          <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6\">\n            {[\n              { name: 'Instagram', icon: '📷', color: 'bg-pink-500' },\n              { name: 'TikTok', icon: '🎵', color: 'bg-black' },\n              { name: 'YouTube', icon: '📺', color: 'bg-red-500' },\n              { name: 'Facebook', icon: '📘', color: 'bg-blue-500' },\n              { name: 'Twitter', icon: '🐦', color: 'bg-blue-400' },\n              { name: 'Snapchat', icon: '👻', color: 'bg-yellow-400' }\n            ].map((platform, index) => (\n              <div key={index} className=\"bg-white p-6 rounded-lg shadow text-center hover:shadow-md transition-shadow\">\n                <div className={`w-12 h-12 ${platform.color} rounded-lg flex items-center justify-center mx-auto mb-3`}>\n                  <span className=\"text-2xl\">{platform.icon}</span>\n                </div>\n                <h3 className=\"font-medium text-gray-900\">{platform.name}</h3>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n\n      {/* CTA Section */}\n      <div className=\"bg-purple-600 py-16\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n          <h2 className=\"text-3xl font-bold text-white mb-4\">Ready to Boost Your Social Media?</h2>\n          <p className=\"text-xl text-purple-100 mb-8\">Join thousands of satisfied customers and start growing today!</p>\n          <Link href=\"/auth/register\" className=\"bg-white text-purple-600 px-8 py-4 rounded-lg font-semibold text-lg hover:bg-gray-100 transition-colors\">\n            Get Started Now\n          </Link>\n        </div>\n      </div>\n\n      {/* Footer */}\n      <footer className=\"bg-gray-900 text-white py-12\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n            <div>\n              <div className=\"flex items-center space-x-2 mb-4\">\n                <div className=\"w-8 h-8 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg flex items-center justify-center\">\n                  <span className=\"text-white font-bold text-sm\">T</span>\n                </div>\n                <span className=\"text-xl font-bold\">Trendz</span>\n              </div>\n              <p className=\"text-gray-400\">Professional SMM panel for all your social media marketing needs.</p>\n            </div>\n\n            <div>\n              <h3 className=\"text-lg font-semibold mb-4\">Services</h3>\n              <ul className=\"space-y-2 text-gray-400\">\n                <li><Link href=\"/services\" className=\"hover:text-white\">All Services</Link></li>\n                <li><Link href=\"/order\" className=\"hover:text-white\">New Order</Link></li>\n                <li><Link href=\"/orders\" className=\"hover:text-white\">Track Orders</Link></li>\n              </ul>\n            </div>\n\n            <div>\n              <h3 className=\"text-lg font-semibold mb-4\">Support</h3>\n              <ul className=\"space-y-2 text-gray-400\">\n                <li><Link href=\"/support\" className=\"hover:text-white\">Contact Support</Link></li>\n                <li><Link href=\"/faq\" className=\"hover:text-white\">FAQ</Link></li>\n                <li><Link href=\"/terms\" className=\"hover:text-white\">Terms of Service</Link></li>\n                <li><Link href=\"/privacy\" className=\"hover:text-white\">Privacy Policy</Link></li>\n              </ul>\n            </div>\n\n            <div>\n              <h3 className=\"text-lg font-semibold mb-4\">Account</h3>\n              <ul className=\"space-y-2 text-gray-400\">\n                <li><Link href=\"/auth/login\" className=\"hover:text-white\">Login</Link></li>\n                <li><Link href=\"/auth/register\" className=\"hover:text-white\">Register</Link></li>\n                <li><Link href=\"/dashboard\" className=\"hover:text-white\">Dashboard</Link></li>\n                <li><Link href=\"/profile\" className=\"hover:text-white\">Profile</Link></li>\n              </ul>\n            </div>\n          </div>\n\n          <div className=\"border-t border-gray-800 mt-8 pt-8 text-center text-gray-400\">\n            <p>&copy; 2025 Trendz SMM Panel. All rights reserved.</p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;;sDACvB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;sDAEjD,8OAAC;4CAAK,WAAU;sDAAkC;;;;;;;;;;;;;;;;;0CAItD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAY,WAAU;kDAAsC;;;;;;kDACvE,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAO,WAAU;kDAAsC;;;;;;kDAClE,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAsC;;;;;;kDACtE,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAa,WAAU;kDAAsC;;;;;;;;;;;;0CAG1E,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAc,WAAU;kDAAsC;;;;;;kDACzE,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAiB,WAAU;kDAAsF;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASpI,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAK,WAAU;kDAAQ;;;;;;kDACxB,8OAAC;wCAAK,WAAU;kDAAqF;;;;;;;;;;;;0CAIvG,8OAAC;gCAAE,WAAU;0CAAiD;;;;;;0CAG9D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAY,WAAU;kDAA0G;;;;;;kDAG3I,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAiB,WAAU;kDAAqI;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASnL,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAGvC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAA0B,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACjF,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAG/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAAyB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAChF,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAG/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAAwB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAC/E,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOrC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAGvC,8OAAC;4BAAI,WAAU;sCACZ;gCACC;oCAAE,MAAM;oCAAa,MAAM;oCAAM,OAAO;gCAAc;gCACtD;oCAAE,MAAM;oCAAU,MAAM;oCAAM,OAAO;gCAAW;gCAChD;oCAAE,MAAM;oCAAW,MAAM;oCAAM,OAAO;gCAAa;gCACnD;oCAAE,MAAM;oCAAY,MAAM;oCAAM,OAAO;gCAAc;gCACrD;oCAAE,MAAM;oCAAW,MAAM;oCAAM,OAAO;gCAAc;gCACpD;oCAAE,MAAM;oCAAY,MAAM;oCAAM,OAAO;gCAAgB;6BACxD,CAAC,GAAG,CAAC,CAAC,UAAU,sBACf,8OAAC;oCAAgB,WAAU;;sDACzB,8OAAC;4CAAI,WAAW,CAAC,UAAU,EAAE,SAAS,KAAK,CAAC,yDAAyD,CAAC;sDACpG,cAAA,8OAAC;gDAAK,WAAU;0DAAY,SAAS,IAAI;;;;;;;;;;;sDAE3C,8OAAC;4CAAG,WAAU;sDAA6B,SAAS,IAAI;;;;;;;mCAJhD;;;;;;;;;;;;;;;;;;;;;0BAYlB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAqC;;;;;;sCACnD,8OAAC;4BAAE,WAAU;sCAA+B;;;;;;sCAC5C,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAiB,WAAU;sCAA0G;;;;;;;;;;;;;;;;;0BAOpJ,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAA+B;;;;;;;;;;;8DAEjD,8OAAC;oDAAK,WAAU;8DAAoB;;;;;;;;;;;;sDAEtC,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAG/B,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAY,WAAU;kEAAmB;;;;;;;;;;;8DACxD,8OAAC;8DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAS,WAAU;kEAAmB;;;;;;;;;;;8DACrD,8OAAC;8DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAU,WAAU;kEAAmB;;;;;;;;;;;;;;;;;;;;;;;8CAI1D,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAW,WAAU;kEAAmB;;;;;;;;;;;8DACvD,8OAAC;8DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAO,WAAU;kEAAmB;;;;;;;;;;;8DACnD,8OAAC;8DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAS,WAAU;kEAAmB;;;;;;;;;;;8DACrD,8OAAC;8DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAW,WAAU;kEAAmB;;;;;;;;;;;;;;;;;;;;;;;8CAI3D,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAc,WAAU;kEAAmB;;;;;;;;;;;8DAC1D,8OAAC;8DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAiB,WAAU;kEAAmB;;;;;;;;;;;8DAC7D,8OAAC;8DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAa,WAAU;kEAAmB;;;;;;;;;;;8DACzD,8OAAC;8DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAW,WAAU;kEAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAK7D,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;0CAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMf", "debugId": null}}]}