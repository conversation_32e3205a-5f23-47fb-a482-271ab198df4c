export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  username: string;
  phone?: string;
  status: 'active' | 'suspended' | 'pending';
  role: 'user' | 'admin';
  emailVerified: boolean;
  phoneVerified: boolean;
  twoFactorEnabled: boolean;
  balance: number;
  totalSpent: number;
  totalOrders: number;
  joinDate: Date;
  lastLogin?: Date;
  lastActivity?: Date;
  timezone: string;
  language: string;
  notifications: {
    email: boolean;
    push: boolean;
    orderUpdates: boolean;
    promotions: boolean;
    security: boolean;
  };
  preferences: {
    currency: string;
    theme: 'light' | 'dark' | 'system';
  };
  metadata?: any;
}

export interface UserActivity {
  id: string;
  userId: string;
  type: 'login' | 'logout' | 'order' | 'payment' | 'profile_update' | 'password_change';
  description: string;
  ipAddress: string;
  userAgent: string;
  timestamp: Date;
  metadata?: any;
}

export interface UserStats {
  totalUsers: number;
  activeUsers: number;
  suspendedUsers: number;
  pendingUsers: number;
  newUsersToday: number;
  newUsersThisWeek: number;
  newUsersThisMonth: number;
}

export class UserManager {
  private static instance: UserManager;
  private users: Map<string, User> = new Map();
  private activities: Map<string, UserActivity[]> = new Map();
  private sessions: Map<string, { userId: string; expiresAt: Date }> = new Map();

  private constructor() {
    this.initializeMockUsers();
  }

  public static getInstance(): UserManager {
    if (!UserManager.instance) {
      UserManager.instance = new UserManager();
    }
    return UserManager.instance;
  }

  private initializeMockUsers() {
    const mockUsers: User[] = [
      {
        id: 'user1',
        email: '<EMAIL>',
        firstName: 'Ahmed',
        lastName: 'Mohamed',
        username: 'ahmed_mohamed',
        phone: '+201234567890',
        status: 'active',
        role: 'user',
        emailVerified: true,
        phoneVerified: true,
        twoFactorEnabled: false,
        balance: 125.50,
        totalSpent: 450.75,
        totalOrders: 15,
        joinDate: new Date('2025-01-15'),
        lastLogin: new Date('2025-01-30'),
        lastActivity: new Date('2025-01-30'),
        timezone: 'Africa/Cairo',
        language: 'ar',
        notifications: {
          email: true,
          push: true,
          orderUpdates: true,
          promotions: false,
          security: true
        },
        preferences: {
          currency: 'USD',
          theme: 'system'
        }
      },
      {
        id: 'user2',
        email: '<EMAIL>',
        firstName: 'Sara',
        lastName: 'Ali',
        username: 'sara_ali',
        phone: '+201987654321',
        status: 'active',
        role: 'user',
        emailVerified: true,
        phoneVerified: false,
        twoFactorEnabled: true,
        balance: 89.30,
        totalSpent: 234.60,
        totalOrders: 8,
        joinDate: new Date('2025-01-20'),
        lastLogin: new Date('2025-01-29'),
        lastActivity: new Date('2025-01-29'),
        timezone: 'Africa/Cairo',
        language: 'en',
        notifications: {
          email: true,
          push: false,
          orderUpdates: true,
          promotions: true,
          security: true
        },
        preferences: {
          currency: 'USD',
          theme: 'light'
        }
      },
      {
        id: 'admin1',
        email: '<EMAIL>',
        firstName: 'Admin',
        lastName: 'User',
        username: 'admin',
        status: 'active',
        role: 'admin',
        emailVerified: true,
        phoneVerified: true,
        twoFactorEnabled: true,
        balance: 0,
        totalSpent: 0,
        totalOrders: 0,
        joinDate: new Date('2025-01-01'),
        lastLogin: new Date('2025-01-30'),
        lastActivity: new Date('2025-01-30'),
        timezone: 'UTC',
        language: 'en',
        notifications: {
          email: true,
          push: true,
          orderUpdates: true,
          promotions: false,
          security: true
        },
        preferences: {
          currency: 'USD',
          theme: 'dark'
        }
      }
    ];

    mockUsers.forEach(user => {
      this.users.set(user.id, user);
      this.activities.set(user.id, []);
    });
  }

  public async createUser(userData: Partial<User>): Promise<User> {
    const userId = this.generateUserId();
    
    // Check if email already exists
    const existingUser = Array.from(this.users.values()).find(u => u.email === userData.email);
    if (existingUser) {
      throw new Error('Email already exists');
    }

    // Check if username already exists
    if (userData.username) {
      const existingUsername = Array.from(this.users.values()).find(u => u.username === userData.username);
      if (existingUsername) {
        throw new Error('Username already exists');
      }
    }

    const user: User = {
      id: userId,
      email: userData.email!,
      firstName: userData.firstName!,
      lastName: userData.lastName!,
      username: userData.username || this.generateUsername(userData.firstName!, userData.lastName!),
      phone: userData.phone,
      status: 'pending',
      role: 'user',
      emailVerified: false,
      phoneVerified: false,
      twoFactorEnabled: false,
      balance: 0,
      totalSpent: 0,
      totalOrders: 0,
      joinDate: new Date(),
      timezone: userData.timezone || 'UTC',
      language: userData.language || 'en',
      notifications: {
        email: true,
        push: true,
        orderUpdates: true,
        promotions: true,
        security: true
      },
      preferences: {
        currency: 'USD',
        theme: 'system'
      }
    };

    this.users.set(userId, user);
    this.activities.set(userId, []);

    // Log registration activity
    await this.logActivity(userId, 'profile_update', 'User registered', '127.0.0.1', 'Browser');

    console.log(`User ${userId} created: ${user.email}`);
    return user;
  }

  public async updateUser(userId: string, updates: Partial<User>): Promise<User | null> {
    const user = this.users.get(userId);
    if (!user) return null;

    // Validate email uniqueness if being updated
    if (updates.email && updates.email !== user.email) {
      const existingUser = Array.from(this.users.values()).find(u => u.email === updates.email && u.id !== userId);
      if (existingUser) {
        throw new Error('Email already exists');
      }
    }

    // Validate username uniqueness if being updated
    if (updates.username && updates.username !== user.username) {
      const existingUsername = Array.from(this.users.values()).find(u => u.username === updates.username && u.id !== userId);
      if (existingUsername) {
        throw new Error('Username already exists');
      }
    }

    const updatedUser = { ...user, ...updates };
    this.users.set(userId, updatedUser);

    // Log update activity
    await this.logActivity(userId, 'profile_update', 'Profile updated', '127.0.0.1', 'Browser');

    console.log(`User ${userId} updated`);
    return updatedUser;
  }

  public async suspendUser(userId: string, reason: string): Promise<boolean> {
    const user = this.users.get(userId);
    if (!user || user.role === 'admin') return false;

    user.status = 'suspended';
    this.users.set(userId, user);

    // Log suspension activity
    await this.logActivity(userId, 'profile_update', `Account suspended: ${reason}`, '127.0.0.1', 'Admin');

    console.log(`User ${userId} suspended: ${reason}`);
    return true;
  }

  public async activateUser(userId: string): Promise<boolean> {
    const user = this.users.get(userId);
    if (!user) return false;

    user.status = 'active';
    this.users.set(userId, user);

    // Log activation activity
    await this.logActivity(userId, 'profile_update', 'Account activated', '127.0.0.1', 'Admin');

    console.log(`User ${userId} activated`);
    return true;
  }

  public async verifyEmail(userId: string): Promise<boolean> {
    const user = this.users.get(userId);
    if (!user) return false;

    user.emailVerified = true;
    if (user.status === 'pending') {
      user.status = 'active';
    }
    this.users.set(userId, user);

    // Log verification activity
    await this.logActivity(userId, 'profile_update', 'Email verified', '127.0.0.1', 'System');

    console.log(`User ${userId} email verified`);
    return true;
  }

  public async updateBalance(userId: string, amount: number): Promise<boolean> {
    const user = this.users.get(userId);
    if (!user) return false;

    user.balance += amount;
    if (amount < 0) {
      user.totalSpent += Math.abs(amount);
    }
    this.users.set(userId, user);

    return true;
  }

  public async incrementOrderCount(userId: string): Promise<boolean> {
    const user = this.users.get(userId);
    if (!user) return false;

    user.totalOrders += 1;
    this.users.set(userId, user);

    return true;
  }

  public async logActivity(
    userId: string,
    type: UserActivity['type'],
    description: string,
    ipAddress: string,
    userAgent: string,
    metadata?: any
  ): Promise<void> {
    const activity: UserActivity = {
      id: this.generateActivityId(),
      userId,
      type,
      description,
      ipAddress,
      userAgent,
      timestamp: new Date(),
      metadata
    };

    const userActivities = this.activities.get(userId) || [];
    userActivities.unshift(activity); // Add to beginning

    // Keep only last 100 activities per user
    if (userActivities.length > 100) {
      userActivities.splice(100);
    }

    this.activities.set(userId, userActivities);

    // Update last activity
    const user = this.users.get(userId);
    if (user) {
      user.lastActivity = new Date();
      this.users.set(userId, user);
    }
  }

  public getUser(userId: string): User | undefined {
    return this.users.get(userId);
  }

  public getUserByEmail(email: string): User | undefined {
    return Array.from(this.users.values()).find(user => user.email === email);
  }

  public getUserByUsername(username: string): User | undefined {
    return Array.from(this.users.values()).find(user => user.username === username);
  }

  public getAllUsers(): User[] {
    return Array.from(this.users.values());
  }

  public getUserActivities(userId: string): UserActivity[] {
    return this.activities.get(userId) || [];
  }

  public searchUsers(query: string): User[] {
    const lowercaseQuery = query.toLowerCase();
    return Array.from(this.users.values()).filter(user =>
      user.email.toLowerCase().includes(lowercaseQuery) ||
      user.firstName.toLowerCase().includes(lowercaseQuery) ||
      user.lastName.toLowerCase().includes(lowercaseQuery) ||
      user.username.toLowerCase().includes(lowercaseQuery)
    );
  }

  public getUserStats(): UserStats {
    const users = Array.from(this.users.values());
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
    const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);

    return {
      totalUsers: users.length,
      activeUsers: users.filter(u => u.status === 'active').length,
      suspendedUsers: users.filter(u => u.status === 'suspended').length,
      pendingUsers: users.filter(u => u.status === 'pending').length,
      newUsersToday: users.filter(u => u.joinDate >= today).length,
      newUsersThisWeek: users.filter(u => u.joinDate >= weekAgo).length,
      newUsersThisMonth: users.filter(u => u.joinDate >= monthAgo).length
    };
  }

  private generateUserId(): string {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substr(2, 5);
    return `user_${timestamp}${random}`;
  }

  private generateActivityId(): string {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substr(2, 5);
    return `act_${timestamp}${random}`;
  }

  private generateUsername(firstName: string, lastName: string): string {
    const base = `${firstName.toLowerCase()}_${lastName.toLowerCase()}`;
    const random = Math.floor(Math.random() * 1000);
    return `${base}${random}`;
  }
}
