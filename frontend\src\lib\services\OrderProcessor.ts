export interface Order {
  id: string;
  userId: string;
  serviceId: number;
  serviceName: string;
  platform: string;
  category: string;
  quantity: number;
  delivered: number;
  price: number;
  status: 'pending' | 'in_progress' | 'completed' | 'partial' | 'cancelled' | 'refunded';
  link: string;
  startCount: number;
  speed: string;
  provider: string;
  createdAt: Date;
  updatedAt: Date;
  completedAt?: Date;
  notes?: string;
}

export interface Service {
  id: number;
  name: string;
  platform: string;
  category: string;
  price: number;
  minOrder: number;
  maxOrder: number;
  provider: string;
  apiEndpoint: string;
  apiKey: string;
  status: 'active' | 'inactive' | 'maintenance';
  speed: string;
  quality: string;
  refill: boolean;
  refillPeriod: number;
}

export interface Provider {
  id: string;
  name: string;
  apiUrl: string;
  apiKey: string;
  status: 'active' | 'inactive';
  balance: number;
  services: Service[];
}

export class OrderProcessor {
  private static instance: OrderProcessor;
  private orders: Map<string, Order> = new Map();
  private services: Map<number, Service> = new Map();
  private providers: Map<string, Provider> = new Map();
  private processingQueue: string[] = [];
  private isProcessing = false;

  private constructor() {
    this.initializeServices();
    this.initializeProviders();
    this.startProcessing();
  }

  public static getInstance(): OrderProcessor {
    if (!OrderProcessor.instance) {
      OrderProcessor.instance = new OrderProcessor();
    }
    return OrderProcessor.instance;
  }

  private initializeServices() {
    // Mock services data - في التطبيق الحقيقي سيتم جلبها من قاعدة البيانات
    const mockServices: Service[] = [
      {
        id: 1,
        name: 'Instagram Followers - High Quality',
        platform: 'Instagram',
        category: 'Followers',
        price: 2.50,
        minOrder: 100,
        maxOrder: 50000,
        provider: 'provider_a',
        apiEndpoint: '/instagram/followers',
        apiKey: 'service_key_1',
        status: 'active',
        speed: 'Fast',
        quality: 'High Quality',
        refill: true,
        refillPeriod: 30
      },
      {
        id: 2,
        name: 'TikTok Views - Instant',
        platform: 'TikTok',
        category: 'Views',
        price: 0.80,
        minOrder: 1000,
        maxOrder: 1000000,
        provider: 'provider_b',
        apiEndpoint: '/tiktok/views',
        apiKey: 'service_key_2',
        status: 'active',
        speed: 'Instant',
        quality: 'Premium',
        refill: false,
        refillPeriod: 0
      }
    ];

    mockServices.forEach(service => {
      this.services.set(service.id, service);
    });
  }

  private initializeProviders() {
    // Mock providers data
    const mockProviders: Provider[] = [
      {
        id: 'provider_a',
        name: 'Provider A',
        apiUrl: 'https://api.provider-a.com',
        apiKey: 'provider_a_api_key',
        status: 'active',
        balance: 1000.00,
        services: []
      },
      {
        id: 'provider_b',
        name: 'Provider B',
        apiUrl: 'https://api.provider-b.com',
        apiKey: 'provider_b_api_key',
        status: 'active',
        balance: 2500.00,
        services: []
      }
    ];

    mockProviders.forEach(provider => {
      this.providers.set(provider.id, provider);
    });
  }

  public async createOrder(orderData: Partial<Order>): Promise<Order> {
    const orderId = this.generateOrderId();
    const service = this.services.get(orderData.serviceId!);
    
    if (!service) {
      throw new Error('Service not found');
    }

    if (orderData.quantity! < service.minOrder || orderData.quantity! > service.maxOrder) {
      throw new Error(`Quantity must be between ${service.minOrder} and ${service.maxOrder}`);
    }

    const order: Order = {
      id: orderId,
      userId: orderData.userId!,
      serviceId: orderData.serviceId!,
      serviceName: service.name,
      platform: service.platform,
      category: service.category,
      quantity: orderData.quantity!,
      delivered: 0,
      price: this.calculatePrice(service, orderData.quantity!),
      status: 'pending',
      link: orderData.link!,
      startCount: await this.getStartCount(orderData.link!),
      speed: service.speed,
      provider: service.provider,
      createdAt: new Date(),
      updatedAt: new Date(),
      notes: orderData.notes
    };

    this.orders.set(orderId, order);
    this.addToQueue(orderId);

    return order;
  }

  private calculatePrice(service: Service, quantity: number): number {
    const units = Math.ceil(quantity / 1000);
    return units * service.price;
  }

  private async getStartCount(link: string): Promise<number> {
    // Mock function to get current count from social media platform
    // في التطبيق الحقيقي سيتم استخدام APIs الخاصة بكل منصة
    return Math.floor(Math.random() * 10000) + 1000;
  }

  private generateOrderId(): string {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substr(2, 5);
    return `TRZ${timestamp}${random}`.toUpperCase();
  }

  private addToQueue(orderId: string) {
    this.processingQueue.push(orderId);
    console.log(`Order ${orderId} added to processing queue`);
  }

  private async startProcessing() {
    if (this.isProcessing) return;
    
    this.isProcessing = true;
    console.log('Order processing started');

    while (true) {
      if (this.processingQueue.length > 0) {
        const orderId = this.processingQueue.shift()!;
        await this.processOrder(orderId);
      }
      
      // Wait 5 seconds before checking queue again
      await new Promise(resolve => setTimeout(resolve, 5000));
    }
  }

  private async processOrder(orderId: string) {
    const order = this.orders.get(orderId);
    if (!order) return;

    console.log(`Processing order ${orderId}`);

    try {
      // Update order status to in_progress
      order.status = 'in_progress';
      order.updatedAt = new Date();
      this.orders.set(orderId, order);

      const service = this.services.get(order.serviceId);
      const provider = this.providers.get(service!.provider);

      if (!service || !provider) {
        throw new Error('Service or provider not found');
      }

      // Send order to provider API
      const providerOrderId = await this.sendToProvider(order, service, provider);
      
      // Start monitoring order progress
      this.monitorOrderProgress(orderId, providerOrderId, provider);

    } catch (error) {
      console.error(`Error processing order ${orderId}:`, error);
      order.status = 'cancelled';
      order.updatedAt = new Date();
      order.notes = `Error: ${error}`;
      this.orders.set(orderId, order);
    }
  }

  private async sendToProvider(order: Order, service: Service, provider: Provider): Promise<string> {
    // Mock API call to provider
    console.log(`Sending order ${order.id} to ${provider.name}`);
    
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Mock provider order ID
    return `${provider.id}_${Date.now()}`;
  }

  private async monitorOrderProgress(orderId: string, providerOrderId: string, provider: Provider) {
    const order = this.orders.get(orderId);
    if (!order) return;

    // Simulate order progress monitoring
    const totalSteps = 10;
    const stepDelivery = Math.floor(order.quantity / totalSteps);

    for (let step = 1; step <= totalSteps; step++) {
      // Wait between 30 seconds to 2 minutes per step
      const waitTime = Math.random() * 90000 + 30000;
      await new Promise(resolve => setTimeout(resolve, waitTime));

      // Update delivered count
      const delivered = Math.min(step * stepDelivery, order.quantity);
      order.delivered = delivered;
      order.updatedAt = new Date();

      // Check if order is completed
      if (delivered >= order.quantity) {
        order.status = 'completed';
        order.completedAt = new Date();
        console.log(`Order ${orderId} completed`);
        break;
      }

      this.orders.set(orderId, order);
      console.log(`Order ${orderId} progress: ${delivered}/${order.quantity}`);
    }
  }

  public getOrder(orderId: string): Order | undefined {
    return this.orders.get(orderId);
  }

  public getUserOrders(userId: string): Order[] {
    return Array.from(this.orders.values()).filter(order => order.userId === userId);
  }

  public getAllOrders(): Order[] {
    return Array.from(this.orders.values());
  }

  public async cancelOrder(orderId: string): Promise<boolean> {
    const order = this.orders.get(orderId);
    if (!order) return false;

    if (order.status === 'pending') {
      order.status = 'cancelled';
      order.updatedAt = new Date();
      this.orders.set(orderId, order);
      
      // Remove from queue if still pending
      const queueIndex = this.processingQueue.indexOf(orderId);
      if (queueIndex > -1) {
        this.processingQueue.splice(queueIndex, 1);
      }
      
      return true;
    }

    return false;
  }

  public async refillOrder(orderId: string): Promise<boolean> {
    const order = this.orders.get(orderId);
    if (!order) return false;

    const service = this.services.get(order.serviceId);
    if (!service || !service.refill) return false;

    // Check if refill period is still valid
    const daysSinceCompletion = order.completedAt 
      ? (Date.now() - order.completedAt.getTime()) / (1000 * 60 * 60 * 24)
      : 0;

    if (daysSinceCompletion > service.refillPeriod) {
      return false;
    }

    // Create refill order
    const currentCount = await this.getStartCount(order.link);
    const expectedCount = order.startCount + order.quantity;
    
    if (currentCount < expectedCount) {
      const refillQuantity = expectedCount - currentCount;
      
      // Create new order for refill
      await this.createOrder({
        userId: order.userId,
        serviceId: order.serviceId,
        quantity: refillQuantity,
        link: order.link,
        notes: `Refill for order ${orderId}`
      });
      
      return true;
    }

    return false;
  }

  public getQueueStatus(): { pending: number, processing: number } {
    const pendingOrders = Array.from(this.orders.values()).filter(o => o.status === 'pending').length;
    const processingOrders = Array.from(this.orders.values()).filter(o => o.status === 'in_progress').length;
    
    return {
      pending: pendingOrders,
      processing: processingOrders
    };
  }
}
