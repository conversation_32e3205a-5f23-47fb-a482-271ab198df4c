import { OrderProcessor } from './OrderProcessor';
import { PaymentProcessor } from './PaymentProcessor';
import { UserManager } from './UserManager';

export interface SystemStats {
  users: {
    total: number;
    active: number;
    newToday: number;
  };
  orders: {
    total: number;
    pending: number;
    processing: number;
    completed: number;
  };
  payments: {
    totalRevenue: number;
    totalTransactions: number;
    pendingAmount: number;
  };
  system: {
    uptime: number;
    version: string;
    status: 'operational' | 'maintenance' | 'degraded';
  };
}

export interface SystemNotification {
  id: string;
  type: 'info' | 'warning' | 'error' | 'success';
  title: string;
  message: string;
  timestamp: Date;
  read: boolean;
  userId?: string; // If null, it's a system-wide notification
}

export class SystemManager {
  private static instance: SystemManager;
  private orderProcessor: OrderProcessor;
  private paymentProcessor: PaymentProcessor;
  private userManager: UserManager;
  private notifications: Map<string, SystemNotification> = new Map();
  private systemStatus: 'operational' | 'maintenance' | 'degraded' = 'operational';
  private startTime: Date = new Date();

  private constructor() {
    this.orderProcessor = OrderProcessor.getInstance();
    this.paymentProcessor = PaymentProcessor.getInstance();
    this.userManager = UserManager.getInstance();
    this.initializeSystem();
  }

  public static getInstance(): SystemManager {
    if (!SystemManager.instance) {
      SystemManager.instance = new SystemManager();
    }
    return SystemManager.instance;
  }

  private initializeSystem() {
    console.log('System Manager initialized');
    
    // Start system monitoring
    this.startSystemMonitoring();
    
    // Initialize system notifications
    this.createSystemNotification(
      'info',
      'System Started',
      'Trendz SMM Panel system has been started successfully'
    );
  }

  private startSystemMonitoring() {
    // Monitor system health every 5 minutes
    setInterval(() => {
      this.performHealthCheck();
    }, 5 * 60 * 1000);

    // Generate daily reports
    setInterval(() => {
      this.generateDailyReport();
    }, 24 * 60 * 60 * 1000);
  }

  private performHealthCheck() {
    const stats = this.getSystemStats();
    
    // Check for high pending orders
    if (stats.orders.pending > 50) {
      this.createSystemNotification(
        'warning',
        'High Pending Orders',
        `There are ${stats.orders.pending} pending orders that need attention`
      );
    }

    // Check for high pending payments
    if (stats.payments.pendingAmount > 1000) {
      this.createSystemNotification(
        'warning',
        'High Pending Payments',
        `$${stats.payments.pendingAmount.toFixed(2)} in pending payments need review`
      );
    }

    console.log('System health check completed');
  }

  private generateDailyReport() {
    const stats = this.getSystemStats();
    
    this.createSystemNotification(
      'info',
      'Daily Report',
      `Today: ${stats.users.newToday} new users, ${stats.orders.completed} completed orders, $${stats.payments.totalRevenue.toFixed(2)} revenue`
    );
  }

  public getSystemStats(): SystemStats {
    const userStats = this.userManager.getUserStats();
    const paymentStats = this.paymentProcessor.getPaymentStats();
    const queueStatus = this.orderProcessor.getQueueStatus();
    const allOrders = this.orderProcessor.getAllOrders();

    return {
      users: {
        total: userStats.totalUsers,
        active: userStats.activeUsers,
        newToday: userStats.newUsersToday
      },
      orders: {
        total: allOrders.length,
        pending: queueStatus.pending,
        processing: queueStatus.processing,
        completed: allOrders.filter(o => o.status === 'completed').length
      },
      payments: {
        totalRevenue: paymentStats.totalRevenue,
        totalTransactions: paymentStats.totalTransactions,
        pendingAmount: paymentStats.pendingAmount
      },
      system: {
        uptime: Date.now() - this.startTime.getTime(),
        version: '1.0.0',
        status: this.systemStatus
      }
    };
  }

  public createSystemNotification(
    type: SystemNotification['type'],
    title: string,
    message: string,
    userId?: string
  ): SystemNotification {
    const notification: SystemNotification = {
      id: this.generateNotificationId(),
      type,
      title,
      message,
      timestamp: new Date(),
      read: false,
      userId
    };

    this.notifications.set(notification.id, notification);
    console.log(`System notification created: ${title}`);
    
    return notification;
  }

  public getNotifications(userId?: string): SystemNotification[] {
    return Array.from(this.notifications.values())
      .filter(notification => 
        !userId || !notification.userId || notification.userId === userId
      )
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  }

  public markNotificationAsRead(notificationId: string): boolean {
    const notification = this.notifications.get(notificationId);
    if (!notification) return false;

    notification.read = true;
    this.notifications.set(notificationId, notification);
    return true;
  }

  public deleteNotification(notificationId: string): boolean {
    return this.notifications.delete(notificationId);
  }

  public setSystemStatus(status: 'operational' | 'maintenance' | 'degraded', reason?: string) {
    const oldStatus = this.systemStatus;
    this.systemStatus = status;

    if (oldStatus !== status) {
      this.createSystemNotification(
        status === 'operational' ? 'success' : 'warning',
        'System Status Changed',
        `System status changed from ${oldStatus} to ${status}${reason ? `: ${reason}` : ''}`
      );
    }
  }

  public getSystemStatus(): 'operational' | 'maintenance' | 'degraded' {
    return this.systemStatus;
  }

  public async processNewOrder(orderData: any): Promise<any> {
    try {
      // Validate user balance
      const user = this.userManager.getUser(orderData.userId);
      if (!user) {
        throw new Error('User not found');
      }

      if (user.balance < orderData.price) {
        throw new Error('Insufficient balance');
      }

      // Create order
      const order = await this.orderProcessor.createOrder(orderData);

      // Process payment
      await this.paymentProcessor.processOrderPayment(
        orderData.userId,
        order.id,
        orderData.price
      );

      // Update user stats
      await this.userManager.incrementOrderCount(orderData.userId);

      // Log activity
      await this.userManager.logActivity(
        orderData.userId,
        'order',
        `Order created: ${order.serviceName}`,
        '127.0.0.1',
        'Browser',
        { orderId: order.id, amount: orderData.price }
      );

      // Create notification
      this.createSystemNotification(
        'success',
        'New Order',
        `Order ${order.id} created for ${order.serviceName}`,
        orderData.userId
      );

      return order;
    } catch (error) {
      console.error('Error processing new order:', error);
      throw error;
    }
  }

  public async processDeposit(userId: string, amount: number, methodId: string): Promise<any> {
    try {
      const payment = await this.paymentProcessor.createDeposit(userId, amount, methodId);

      // Log activity
      await this.userManager.logActivity(
        userId,
        'payment',
        `Deposit initiated: $${amount} via ${payment.method}`,
        '127.0.0.1',
        'Browser',
        { paymentId: payment.id, amount, method: methodId }
      );

      // Create notification
      this.createSystemNotification(
        'info',
        'Deposit Initiated',
        `Deposit of $${amount} via ${payment.method} has been initiated`,
        userId
      );

      return payment;
    } catch (error) {
      console.error('Error processing deposit:', error);
      throw error;
    }
  }

  public async processRefund(orderId: string, reason: string): Promise<any> {
    try {
      const order = this.orderProcessor.getOrder(orderId);
      if (!order) {
        throw new Error('Order not found');
      }

      const refund = await this.paymentProcessor.processRefund(
        order.userId,
        orderId,
        order.price,
        reason
      );

      // Update order status
      order.status = 'refunded';
      order.notes = reason;

      // Log activity
      await this.userManager.logActivity(
        order.userId,
        'payment',
        `Refund processed: $${order.price} for order ${orderId}`,
        '127.0.0.1',
        'Admin',
        { orderId, refundId: refund.id, amount: order.price, reason }
      );

      // Create notification
      this.createSystemNotification(
        'info',
        'Refund Processed',
        `Refund of $${order.price} has been processed for order ${orderId}`,
        order.userId
      );

      return refund;
    } catch (error) {
      console.error('Error processing refund:', error);
      throw error;
    }
  }

  public getOrderProcessor(): OrderProcessor {
    return this.orderProcessor;
  }

  public getPaymentProcessor(): PaymentProcessor {
    return this.paymentProcessor;
  }

  public getUserManager(): UserManager {
    return this.userManager;
  }

  private generateNotificationId(): string {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substr(2, 5);
    return `notif_${timestamp}${random}`;
  }

  public getUptime(): number {
    return Date.now() - this.startTime.getTime();
  }

  public getUptimeString(): string {
    const uptime = this.getUptime();
    const days = Math.floor(uptime / (1000 * 60 * 60 * 24));
    const hours = Math.floor((uptime % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((uptime % (1000 * 60 * 60)) / (1000 * 60));

    return `${days}d ${hours}h ${minutes}m`;
  }

  public async backup(): Promise<any> {
    const backupData = {
      users: this.userManager.getAllUsers(),
      orders: this.orderProcessor.getAllOrders(),
      payments: this.paymentProcessor.getAllPayments(),
      notifications: Array.from(this.notifications.values()),
      timestamp: new Date(),
      version: '1.0.0'
    };

    console.log('System backup created');
    return backupData;
  }

  public async restore(backupData: any): Promise<boolean> {
    try {
      // In a real application, this would restore from backup
      console.log('System restore initiated');
      
      this.createSystemNotification(
        'info',
        'System Restored',
        'System has been restored from backup'
      );

      return true;
    } catch (error) {
      console.error('Error restoring system:', error);
      return false;
    }
  }
}
