module.exports = {

"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/action-async-storage.external.js [external] (next/dist/server/app-render/action-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/action-async-storage.external.js", () => require("next/dist/server/app-render/action-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/path [external] (path, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/fs [external] (fs, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/assert [external] (assert, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}}),
"[externals]/tty [external] (tty, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tty", () => require("tty"));

module.exports = mod;
}}),
"[externals]/os [external] (os, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("os", () => require("os"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[project]/src/lib/api.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/js-cookie/dist/js.cookie.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hot-toast/dist/index.mjs [app-ssr] (ecmascript)");
;
;
;
class ApiClient {
    client;
    baseURL;
    constructor(){
        this.baseURL = ("TURBOPACK compile-time value", "http://localhost:5000/api") || 'http://localhost:5000/api';
        this.client = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].create({
            baseURL: this.baseURL,
            timeout: 30000,
            headers: {
                'Content-Type': 'application/json'
            }
        });
        this.setupInterceptors();
    }
    setupInterceptors() {
        // Request interceptor
        this.client.interceptors.request.use((config)=>{
            const token = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get('token');
            if (token) {
                config.headers.Authorization = `Bearer ${token}`;
            }
            // Add language header
            const language = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get('i18next') || 'en';
            config.headers['Accept-Language'] = language;
            return config;
        }, (error)=>{
            return Promise.reject(error);
        });
        // Response interceptor
        this.client.interceptors.response.use((response)=>{
            return response;
        }, (error)=>{
            if (error.response?.status === 401) {
                // Unauthorized - clear token and redirect to login
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].remove('token');
                if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
                ;
            } else if (error.response?.status >= 500) {
                // Server error
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].error('Server error. Please try again later.');
            } else if (error.code === 'ECONNABORTED') {
                // Timeout
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].error('Request timeout. Please try again.');
            } else if (!error.response) {
                // Network error
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].error('Network error. Please check your connection.');
            }
            return Promise.reject(error);
        });
    }
    // Generic request method
    async request(config) {
        try {
            const response = await this.client.request(config);
            return response.data;
        } catch (error) {
            const errorMessage = error.response?.data?.error || error.message || 'An error occurred';
            throw new Error(errorMessage);
        }
    }
    // Auth endpoints
    async login(credentials) {
        return this.request({
            method: 'POST',
            url: '/auth/login',
            data: credentials
        });
    }
    async register(data) {
        return this.request({
            method: 'POST',
            url: '/auth/register',
            data
        });
    }
    async logout() {
        return this.request({
            method: 'POST',
            url: '/auth/logout'
        });
    }
    async getMe() {
        return this.request({
            method: 'GET',
            url: '/auth/me'
        });
    }
    async updateProfile(data) {
        return this.request({
            method: 'PUT',
            url: '/auth/profile',
            data
        });
    }
    async changePassword(data) {
        return this.request({
            method: 'PUT',
            url: '/auth/change-password',
            data
        });
    }
    async forgotPassword(email) {
        return this.request({
            method: 'POST',
            url: '/auth/forgot-password',
            data: {
                email
            }
        });
    }
    async resetPassword(data) {
        return this.request({
            method: 'POST',
            url: '/auth/reset-password',
            data
        });
    }
    // Services endpoints
    async getServices(params) {
        return this.request({
            method: 'GET',
            url: '/services',
            params
        });
    }
    async getService(id) {
        return this.request({
            method: 'GET',
            url: `/services/${id}`
        });
    }
    async calculatePrice(data) {
        return this.request({
            method: 'POST',
            url: '/services/calculate-price',
            data
        });
    }
    // Orders endpoints
    async createOrder(data) {
        return this.request({
            method: 'POST',
            url: '/orders',
            data
        });
    }
    async getOrders(params) {
        return this.request({
            method: 'GET',
            url: '/orders',
            params
        });
    }
    async getOrder(id) {
        return this.request({
            method: 'GET',
            url: `/orders/${id}`
        });
    }
    async cancelOrder(id, reason) {
        return this.request({
            method: 'PATCH',
            url: `/orders/${id}/cancel`,
            data: {
                reason
            }
        });
    }
    async requestRefill(id, reason) {
        return this.request({
            method: 'PATCH',
            url: `/orders/${id}/refill`,
            data: {
                reason
            }
        });
    }
    async getOrderStatistics(params) {
        return this.request({
            method: 'GET',
            url: '/orders/statistics',
            params
        });
    }
    // Payments endpoints
    async createPayment(data) {
        return this.request({
            method: 'POST',
            url: '/payments',
            data
        });
    }
    async getPayments(params) {
        return this.request({
            method: 'GET',
            url: '/payments',
            params
        });
    }
    async getPayment(id) {
        return this.request({
            method: 'GET',
            url: `/payments/${id}`
        });
    }
    async confirmManualPayment(id, details) {
        return this.request({
            method: 'PATCH',
            url: `/payments/${id}/confirm`,
            data: details
        });
    }
    async getPaymentMethods() {
        return this.request({
            method: 'GET',
            url: '/payments/methods'
        });
    }
    async getPaymentStatistics(params) {
        return this.request({
            method: 'GET',
            url: '/payments/statistics',
            params
        });
    }
    // PayPal endpoints
    async createPaypalOrder(data) {
        return this.request({
            method: 'POST',
            url: '/payments/paypal/create-order',
            data
        });
    }
    async capturePaypalOrder(orderID) {
        return this.request({
            method: 'POST',
            url: `/payments/paypal/capture-order/${orderID}`
        });
    }
    // User endpoints
    async getUserBalance() {
        return this.request({
            method: 'GET',
            url: '/users/balance'
        });
    }
    async getUserOrders(params) {
        return this.request({
            method: 'GET',
            url: '/users/orders',
            params
        });
    }
    async getUserPayments(params) {
        return this.request({
            method: 'GET',
            url: '/users/payments',
            params
        });
    }
    async getUserStatistics(params) {
        return this.request({
            method: 'GET',
            url: '/users/statistics',
            params
        });
    }
    async updateNotificationSettings(settings) {
        return this.request({
            method: 'PUT',
            url: '/users/notifications',
            data: settings
        });
    }
    async uploadAvatar(file) {
        const formData = new FormData();
        formData.append('avatar', file);
        return this.request({
            method: 'POST',
            url: '/users/avatar',
            data: formData,
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        });
    }
    async deleteAvatar() {
        return this.request({
            method: 'DELETE',
            url: '/users/avatar'
        });
    }
}
// Create and export a singleton instance
const apiClient = new ApiClient();
const __TURBOPACK__default__export__ = apiClient;
}),
"[project]/src/contexts/AuthContext.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "AuthProvider": ()=>AuthProvider,
    "default": ()=>__TURBOPACK__default__export__,
    "useAuth": ()=>useAuth
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/js-cookie/dist/js.cookie.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hot-toast/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
;
const AuthContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(undefined);
const AuthProvider = ({ children })=>{
    const [user, setUser] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [token, setToken] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(true);
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRouter"])();
    // Initialize auth state
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const initAuth = async ()=>{
            const savedToken = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get('token');
            if (savedToken) {
                setToken(savedToken);
                try {
                    const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].getMe();
                    if (response.success && response.data) {
                        setUser(response.data.user);
                    } else {
                        // Invalid token, clear it
                        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].remove('token');
                        setToken(null);
                    }
                } catch (error) {
                    console.error('Failed to get user info:', error);
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].remove('token');
                    setToken(null);
                }
            }
            setIsLoading(false);
        };
        initAuth();
    }, []);
    const login = async (credentials)=>{
        try {
            setIsLoading(true);
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].login(credentials);
            if (response.success && response.data) {
                const { token: newToken, user: userData } = response.data;
                // Save token to cookies
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].set('token', newToken, {
                    expires: 7,
                    secure: ("TURBOPACK compile-time value", "development") === 'production',
                    sameSite: 'strict'
                });
                setToken(newToken);
                setUser(userData);
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].success('Login successful!');
                // Redirect based on user role
                if (userData.role === 'admin') {
                    router.push('/admin');
                } else {
                    router.push('/dashboard');
                }
            } else {
                throw new Error(response.error || 'Login failed');
            }
        } catch (error) {
            console.error('Login error:', error);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].error(error.message || 'Login failed');
            throw error;
        } finally{
            setIsLoading(false);
        }
    };
    const register = async (data)=>{
        try {
            setIsLoading(true);
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].register(data);
            if (response.success && response.data) {
                const { token: newToken, user: userData } = response.data;
                // Save token to cookies
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].set('token', newToken, {
                    expires: 7,
                    secure: ("TURBOPACK compile-time value", "development") === 'production',
                    sameSite: 'strict'
                });
                setToken(newToken);
                setUser(userData);
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].success('Registration successful!');
                router.push('/dashboard');
            } else {
                throw new Error(response.error || 'Registration failed');
            }
        } catch (error) {
            console.error('Registration error:', error);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].error(error.message || 'Registration failed');
            throw error;
        } finally{
            setIsLoading(false);
        }
    };
    const logout = async ()=>{
        try {
            // Call logout endpoint
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].logout();
        } catch (error) {
            console.error('Logout error:', error);
        } finally{
            // Clear local state regardless of API call result
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].remove('token');
            setToken(null);
            setUser(null);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].success('Logged out successfully');
            router.push('/');
        }
    };
    const updateProfile = async (data)=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].updateProfile(data);
            if (response.success && response.data) {
                setUser(response.data.user);
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].success('Profile updated successfully');
            } else {
                throw new Error(response.error || 'Profile update failed');
            }
        } catch (error) {
            console.error('Profile update error:', error);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].error(error.message || 'Profile update failed');
            throw error;
        }
    };
    const value = {
        user,
        token,
        isLoading,
        isAuthenticated: !!user && !!token,
        login,
        register,
        logout,
        updateProfile
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(AuthContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/AuthContext.tsx",
        lineNumber: 166,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
const useAuth = ()=>{
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(AuthContext);
    if (context === undefined) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
};
const __TURBOPACK__default__export__ = AuthContext;
}),
"[project]/src/locales/en.json (json)": ((__turbopack_context__) => {

__turbopack_context__.v(JSON.parse("{\"common\":{\"loading\":\"Loading...\",\"error\":\"Error\",\"success\":\"Success\",\"cancel\":\"Cancel\",\"save\":\"Save\",\"edit\":\"Edit\",\"delete\":\"Delete\",\"view\":\"View\",\"search\":\"Search\",\"filter\":\"Filter\",\"sort\":\"Sort\",\"next\":\"Next\",\"previous\":\"Previous\",\"submit\":\"Submit\",\"close\":\"Close\",\"confirm\":\"Confirm\",\"yes\":\"Yes\",\"no\":\"No\"},\"navigation\":{\"home\":\"Home\",\"services\":\"Services\",\"orders\":\"Orders\",\"payments\":\"Payments\",\"profile\":\"Profile\",\"dashboard\":\"Dashboard\",\"support\":\"Support\",\"faq\":\"FAQ\",\"login\":\"Login\",\"register\":\"Sign Up\",\"logout\":\"Logout\",\"admin\":\"Admin\"},\"auth\":{\"login\":\"Login\",\"register\":\"Register\",\"email\":\"Email\",\"password\":\"Password\",\"confirmPassword\":\"Confirm Password\",\"firstName\":\"First Name\",\"lastName\":\"Last Name\",\"username\":\"Username\",\"phone\":\"Phone\",\"forgotPassword\":\"Forgot Password?\",\"rememberMe\":\"Remember Me\",\"alreadyHaveAccount\":\"Already have an account?\",\"dontHaveAccount\":\"Don't have an account?\",\"createAccount\":\"Create Account\",\"signIn\":\"Sign In\",\"agreeToTerms\":\"I agree to the Terms of Service and Privacy Policy\"},\"dashboard\":{\"title\":\"Dashboard\",\"welcome\":\"Welcome back\",\"currentBalance\":\"Current Balance\",\"totalOrders\":\"Total Orders\",\"totalSpent\":\"Total Spent\",\"activeOrders\":\"Active Orders\",\"recentOrders\":\"Recent Orders\",\"quickActions\":\"Quick Actions\",\"browseServices\":\"Browse Services\",\"addFunds\":\"Add Funds\",\"viewOrders\":\"View Orders\",\"needHelp\":\"Need Help?\",\"contactSupport\":\"Contact Support\"},\"services\":{\"title\":\"Services\",\"choosePlatform\":\"Choose Your Platform\",\"orderNow\":\"Order Now\",\"price\":\"Price\",\"minOrder\":\"Min Order\",\"maxOrder\":\"Max Order\",\"quality\":\"Quality\",\"speed\":\"Speed\",\"description\":\"Description\",\"features\":\"Features\",\"serviceDetails\":\"Service Details\"},\"orders\":{\"title\":\"My Orders\",\"orderNumber\":\"Order #\",\"status\":\"Status\",\"quantity\":\"Quantity\",\"delivered\":\"Delivered\",\"remains\":\"Remains\",\"startCount\":\"Start Count\",\"progress\":\"Progress\",\"completed\":\"Completed\",\"inProgress\":\"In Progress\",\"pending\":\"Pending\",\"cancelled\":\"Cancelled\",\"partial\":\"Partial\",\"refunded\":\"Refunded\",\"viewDetails\":\"View Details\",\"cancelOrder\":\"Cancel Order\",\"requestRefill\":\"Request Refill\",\"reorder\":\"Reorder\"},\"payments\":{\"title\":\"Payments & Billing\",\"addFunds\":\"Add Funds\",\"paymentMethod\":\"Payment Method\",\"amount\":\"Amount\",\"paymentHistory\":\"Payment History\",\"totalDeposited\":\"Total Deposited\",\"deposit\":\"Deposit\",\"order\":\"Order\",\"fees\":\"Fees\",\"processingTime\":\"Processing Time\",\"availability\":\"Availability\"},\"profile\":{\"title\":\"Profile\",\"profileInformation\":\"Profile Information\",\"security\":\"Security\",\"notifications\":\"Notifications\",\"language\":\"Language\",\"timezone\":\"Timezone\",\"changePassword\":\"Change Password\",\"currentPassword\":\"Current Password\",\"newPassword\":\"New Password\",\"twoFactorAuth\":\"Two-Factor Authentication\",\"emailNotifications\":\"Email Notifications\",\"pushNotifications\":\"Push Notifications\",\"orderUpdates\":\"Order Updates\",\"promotions\":\"Promotions\",\"securityAlerts\":\"Security Alerts\"},\"support\":{\"title\":\"Support\",\"howCanWeHelp\":\"How can we help you?\",\"liveChat\":\"Live Chat\",\"emailSupport\":\"Email Support\",\"whatsapp\":\"WhatsApp\",\"phoneSupport\":\"Phone Support\",\"contactForm\":\"Contact Form\",\"sendMessage\":\"Send Message\",\"subject\":\"Subject\",\"message\":\"Message\",\"priority\":\"Priority\",\"category\":\"Category\",\"systemStatus\":\"System Status\",\"allSystemsOperational\":\"All Systems Operational\"},\"admin\":{\"title\":\"Admin Dashboard\",\"userManagement\":\"User Management\",\"orderManagement\":\"Order Management\",\"serviceManagement\":\"Service Management\",\"paymentManagement\":\"Payment Management\",\"settings\":\"Settings\",\"totalUsers\":\"Total Users\",\"totalRevenue\":\"Total Revenue\",\"monthlyRevenue\":\"Monthly Revenue\",\"activeUsers\":\"Active Users\",\"pendingOrders\":\"Pending Orders\",\"manageUsers\":\"Manage Users\",\"manageOrders\":\"Manage Orders\",\"manageServices\":\"Manage Services\",\"viewPayments\":\"View Payments\"},\"footer\":{\"description\":\"Professional SMM panel for all your social media marketing needs.\",\"allRightsReserved\":\"All rights reserved\",\"termsOfService\":\"Terms of Service\",\"privacyPolicy\":\"Privacy Policy\"},\"theme\":{\"light\":\"Light Mode\",\"dark\":\"Dark Mode\",\"system\":\"System\"},\"language\":{\"english\":\"English\",\"arabic\":\"العربية\"}}"));}),
"[project]/src/locales/ar.json (json)": ((__turbopack_context__) => {

__turbopack_context__.v(JSON.parse("{\"common\":{\"loading\":\"جاري التحميل...\",\"error\":\"خطأ\",\"success\":\"نجح\",\"cancel\":\"إلغاء\",\"save\":\"حفظ\",\"edit\":\"تعديل\",\"delete\":\"حذف\",\"view\":\"عرض\",\"search\":\"بحث\",\"filter\":\"تصفية\",\"sort\":\"ترتيب\",\"next\":\"التالي\",\"previous\":\"السابق\",\"submit\":\"إرسال\",\"close\":\"إغلاق\",\"confirm\":\"تأكيد\",\"yes\":\"نعم\",\"no\":\"لا\"},\"navigation\":{\"home\":\"الرئيسية\",\"services\":\"الخدمات\",\"orders\":\"الطلبات\",\"payments\":\"المدفوعات\",\"profile\":\"الملف الشخصي\",\"dashboard\":\"لوحة التحكم\",\"support\":\"الدعم\",\"faq\":\"الأسئلة الشائعة\",\"login\":\"تسجيل الدخول\",\"register\":\"إنشاء حساب\",\"logout\":\"تسجيل الخروج\",\"admin\":\"الإدارة\"},\"auth\":{\"login\":\"تسجيل الدخول\",\"register\":\"إنشاء حساب\",\"email\":\"البريد الإلكتروني\",\"password\":\"كلمة المرور\",\"confirmPassword\":\"تأكيد كلمة المرور\",\"firstName\":\"الاسم الأول\",\"lastName\":\"اسم العائلة\",\"username\":\"اسم المستخدم\",\"phone\":\"رقم الهاتف\",\"forgotPassword\":\"نسيت كلمة المرور؟\",\"rememberMe\":\"تذكرني\",\"alreadyHaveAccount\":\"لديك حساب بالفعل؟\",\"dontHaveAccount\":\"ليس لديك حساب؟\",\"createAccount\":\"إنشاء حساب\",\"signIn\":\"تسجيل الدخول\",\"agreeToTerms\":\"أوافق على شروط الخدمة وسياسة الخصوصية\"},\"dashboard\":{\"title\":\"لوحة التحكم\",\"welcome\":\"مرحباً بعودتك\",\"currentBalance\":\"الرصيد الحالي\",\"totalOrders\":\"إجمالي الطلبات\",\"totalSpent\":\"إجمالي المصروف\",\"activeOrders\":\"الطلبات النشطة\",\"recentOrders\":\"الطلبات الأخيرة\",\"quickActions\":\"إجراءات سريعة\",\"browseServices\":\"تصفح الخدمات\",\"addFunds\":\"إضافة رصيد\",\"viewOrders\":\"عرض الطلبات\",\"needHelp\":\"تحتاج مساعدة؟\",\"contactSupport\":\"اتصل بالدعم\"},\"services\":{\"title\":\"الخدمات\",\"choosePlatform\":\"اختر منصتك\",\"orderNow\":\"اطلب الآن\",\"price\":\"السعر\",\"minOrder\":\"أقل طلب\",\"maxOrder\":\"أكبر طلب\",\"quality\":\"الجودة\",\"speed\":\"السرعة\",\"description\":\"الوصف\",\"features\":\"المميزات\",\"serviceDetails\":\"تفاصيل الخدمة\"},\"orders\":{\"title\":\"طلباتي\",\"orderNumber\":\"طلب رقم\",\"status\":\"الحالة\",\"quantity\":\"الكمية\",\"delivered\":\"تم التسليم\",\"remains\":\"المتبقي\",\"startCount\":\"العدد الأولي\",\"progress\":\"التقدم\",\"completed\":\"مكتمل\",\"inProgress\":\"قيد التنفيذ\",\"pending\":\"معلق\",\"cancelled\":\"ملغي\",\"partial\":\"جزئي\",\"refunded\":\"مسترد\",\"viewDetails\":\"عرض التفاصيل\",\"cancelOrder\":\"إلغاء الطلب\",\"requestRefill\":\"طلب إعادة تعبئة\",\"reorder\":\"إعادة الطلب\"},\"payments\":{\"title\":\"المدفوعات والفواتير\",\"addFunds\":\"إضافة رصيد\",\"paymentMethod\":\"طريقة الدفع\",\"amount\":\"المبلغ\",\"paymentHistory\":\"تاريخ المدفوعات\",\"totalDeposited\":\"إجمالي الإيداعات\",\"deposit\":\"إيداع\",\"order\":\"طلب\",\"fees\":\"الرسوم\",\"processingTime\":\"وقت المعالجة\",\"availability\":\"التوفر\"},\"profile\":{\"title\":\"الملف الشخصي\",\"profileInformation\":\"معلومات الملف الشخصي\",\"security\":\"الأمان\",\"notifications\":\"الإشعارات\",\"language\":\"اللغة\",\"timezone\":\"المنطقة الزمنية\",\"changePassword\":\"تغيير كلمة المرور\",\"currentPassword\":\"كلمة المرور الحالية\",\"newPassword\":\"كلمة المرور الجديدة\",\"twoFactorAuth\":\"المصادقة الثنائية\",\"emailNotifications\":\"إشعارات البريد الإلكتروني\",\"pushNotifications\":\"الإشعارات المنبثقة\",\"orderUpdates\":\"تحديثات الطلبات\",\"promotions\":\"العروض الترويجية\",\"securityAlerts\":\"تنبيهات الأمان\"},\"support\":{\"title\":\"الدعم\",\"howCanWeHelp\":\"كيف يمكننا مساعدتك؟\",\"liveChat\":\"المحادثة المباشرة\",\"emailSupport\":\"دعم البريد الإلكتروني\",\"whatsapp\":\"واتساب\",\"phoneSupport\":\"الدعم الهاتفي\",\"contactForm\":\"نموذج الاتصال\",\"sendMessage\":\"إرسال رسالة\",\"subject\":\"الموضوع\",\"message\":\"الرسالة\",\"priority\":\"الأولوية\",\"category\":\"الفئة\",\"systemStatus\":\"حالة النظام\",\"allSystemsOperational\":\"جميع الأنظمة تعمل بشكل طبيعي\"},\"admin\":{\"title\":\"لوحة تحكم الإدارة\",\"userManagement\":\"إدارة المستخدمين\",\"orderManagement\":\"إدارة الطلبات\",\"serviceManagement\":\"إدارة الخدمات\",\"paymentManagement\":\"إدارة المدفوعات\",\"settings\":\"الإعدادات\",\"totalUsers\":\"إجمالي المستخدمين\",\"totalRevenue\":\"إجمالي الإيرادات\",\"monthlyRevenue\":\"الإيرادات الشهرية\",\"activeUsers\":\"المستخدمون النشطون\",\"pendingOrders\":\"الطلبات المعلقة\",\"manageUsers\":\"إدارة المستخدمين\",\"manageOrders\":\"إدارة الطلبات\",\"manageServices\":\"إدارة الخدمات\",\"viewPayments\":\"عرض المدفوعات\"},\"footer\":{\"description\":\"لوحة SMM احترافية لجميع احتياجات التسويق عبر وسائل التواصل الاجتماعي.\",\"allRightsReserved\":\"جميع الحقوق محفوظة\",\"termsOfService\":\"شروط الخدمة\",\"privacyPolicy\":\"سياسة الخصوصية\"},\"theme\":{\"light\":\"الوضع الفاتح\",\"dark\":\"الوضع الداكن\",\"system\":\"النظام\"},\"language\":{\"english\":\"English\",\"arabic\":\"العربية\"}}"));}),
"[project]/src/contexts/LanguageContext.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "LanguageProvider": ()=>LanguageProvider,
    "useLanguage": ()=>useLanguage
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$locales$2f$en$2e$json__$28$json$29$__ = __turbopack_context__.i("[project]/src/locales/en.json (json)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$locales$2f$ar$2e$json__$28$json$29$__ = __turbopack_context__.i("[project]/src/locales/ar.json (json)");
'use client';
;
;
;
;
const LanguageContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(undefined);
const translations = {
    en: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$locales$2f$en$2e$json__$28$json$29$__["default"],
    ar: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$locales$2f$ar$2e$json__$28$json$29$__["default"]
};
function LanguageProvider({ children }) {
    const [language, setLanguage] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('en');
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        // Load language from localStorage
        const savedLanguage = localStorage.getItem('language');
        if (savedLanguage && (savedLanguage === 'en' || savedLanguage === 'ar')) {
            setLanguage(savedLanguage);
        } else {
            // Detect browser language
            const browserLang = navigator.language.toLowerCase();
            if (browserLang.startsWith('ar')) {
                setLanguage('ar');
            }
        }
    }, []);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        // Save language to localStorage
        localStorage.setItem('language', language);
        // Update document direction and language
        document.documentElement.lang = language;
        document.documentElement.dir = language === 'ar' ? 'rtl' : 'ltr';
        // Update body class for RTL styling
        if (language === 'ar') {
            document.body.classList.add('rtl');
        } else {
            document.body.classList.remove('rtl');
        }
    }, [
        language
    ]);
    const t = (key)=>{
        const keys = key.split('.');
        let value = translations[language];
        for (const k of keys){
            if (value && typeof value === 'object' && k in value) {
                value = value[k];
            } else {
                // Fallback to English if key not found
                value = translations.en;
                for (const fallbackKey of keys){
                    if (value && typeof value === 'object' && fallbackKey in value) {
                        value = value[fallbackKey];
                    } else {
                        return key; // Return key if not found in fallback
                    }
                }
                break;
            }
        }
        return typeof value === 'string' ? value : key;
    };
    const value = {
        language,
        setLanguage,
        t,
        isRTL: language === 'ar'
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(LanguageContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/LanguageContext.tsx",
        lineNumber: 88,
        columnNumber: 5
    }, this);
}
function useLanguage() {
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(LanguageContext);
    if (context === undefined) {
        throw new Error('useLanguage must be used within a LanguageProvider');
    }
    return context;
}
}),
"[project]/src/contexts/ThemeContext.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "ThemeProvider": ()=>ThemeProvider,
    "useTheme": ()=>useTheme
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
'use client';
;
;
const ThemeContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(undefined);
function ThemeProvider({ children }) {
    const [theme, setTheme] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('system');
    const [isDark, setIsDark] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        // Load theme from localStorage
        const savedTheme = localStorage.getItem('theme');
        if (savedTheme && [
            'light',
            'dark',
            'system'
        ].includes(savedTheme)) {
            setTheme(savedTheme);
        }
    }, []);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const updateTheme = ()=>{
            let shouldBeDark = false;
            if (theme === 'dark') {
                shouldBeDark = true;
            } else if (theme === 'light') {
                shouldBeDark = false;
            } else {
                // System theme
                shouldBeDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
            }
            setIsDark(shouldBeDark);
            // Update document class
            if (shouldBeDark) {
                document.documentElement.classList.add('dark');
            } else {
                document.documentElement.classList.remove('dark');
            }
        };
        updateTheme();
        // Save theme to localStorage
        localStorage.setItem('theme', theme);
        // Listen for system theme changes
        const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
        const handleChange = ()=>{
            if (theme === 'system') {
                updateTheme();
            }
        };
        mediaQuery.addEventListener('change', handleChange);
        return ()=>mediaQuery.removeEventListener('change', handleChange);
    }, [
        theme
    ]);
    const value = {
        theme,
        setTheme,
        isDark
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(ThemeContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/ThemeContext.tsx",
        lineNumber: 74,
        columnNumber: 5
    }, this);
}
function useTheme() {
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(ThemeContext);
    if (context === undefined) {
        throw new Error('useTheme must be used within a ThemeProvider');
    }
    return context;
}
}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__d16600b7._.js.map