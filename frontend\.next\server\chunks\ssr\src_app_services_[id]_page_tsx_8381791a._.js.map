{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/trendz/frontend/src/app/services/%5Bid%5D/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { useParams } from 'next/navigation';\n\nexport default function ServiceDetails() {\n  const params = useParams();\n  const [quantity, setQuantity] = useState('1000');\n  const [link, setLink] = useState('');\n  const [customComments, setCustomComments] = useState(['']);\n  const [showOrderForm, setShowOrderForm] = useState(false);\n\n  // Mock service data - في التطبيق الحقيقي سيتم جلبها من API\n  const service = {\n    id: params.id,\n    name: 'Instagram Followers - High Quality',\n    platform: 'Instagram',\n    category: 'Followers',\n    price: 2.50,\n    priceUnit: 'per_1000',\n    minOrder: 100,\n    maxOrder: 50000,\n    description: 'Get high-quality Instagram followers from real and active accounts. These followers will help boost your social proof and engagement rates.',\n    features: [\n      'Real and active accounts',\n      'High retention rate',\n      'Gradual delivery',\n      'No password required',\n      'Lifetime guarantee'\n    ],\n    requirements: {\n      linkRequired: true,\n      usernameRequired: false,\n      customCommentsRequired: false\n    },\n    averageTime: '1-6 hours',\n    speed: 'Fast',\n    quality: 'High Quality',\n    refillEnabled: true,\n    refillPeriod: 30,\n    cancelEnabled: true,\n    successRate: 98.5,\n    totalOrders: 15420,\n    icon: '📷'\n  };\n\n  const calculatePrice = (qty: number) => {\n    const units = Math.ceil(qty / 1000);\n    return (units * service.price).toFixed(2);\n  };\n\n  const handleQuantityChange = (value: string) => {\n    const numValue = parseInt(value) || 0;\n    if (numValue >= service.minOrder && numValue <= service.maxOrder) {\n      setQuantity(value);\n    }\n  };\n\n  const handleAddComment = () => {\n    if (customComments.length < 10) {\n      setCustomComments([...customComments, '']);\n    }\n  };\n\n  const handleRemoveComment = (index: number) => {\n    if (customComments.length > 1) {\n      setCustomComments(customComments.filter((_, i) => i !== index));\n    }\n  };\n\n  const handleCommentChange = (index: number, value: string) => {\n    const newComments = [...customComments];\n    newComments[index] = value;\n    setCustomComments(newComments);\n  };\n\n  const handleOrderSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    console.log('Order submitted:', {\n      serviceId: service.id,\n      quantity: parseInt(quantity),\n      link,\n      customComments: customComments.filter(comment => comment.trim() !== '')\n    });\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <div className=\"bg-white shadow\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center py-6\">\n            <div className=\"flex items-center\">\n              <Link href=\"/\" className=\"flex items-center space-x-2\">\n                <div className=\"w-8 h-8 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg flex items-center justify-center\">\n                  <span className=\"text-white font-bold text-sm\">T</span>\n                </div>\n                <span className=\"text-xl font-bold text-gray-900\">Trendz</span>\n              </Link>\n            </div>\n            \n            <nav className=\"hidden md:flex space-x-8\">\n              <Link href=\"/dashboard\" className=\"text-gray-600 hover:text-purple-600\">Dashboard</Link>\n              <Link href=\"/services\" className=\"text-purple-600 font-medium\">Services</Link>\n              <Link href=\"/orders\" className=\"text-gray-600 hover:text-purple-600\">Orders</Link>\n              <Link href=\"/payments\" className=\"text-gray-600 hover:text-purple-600\">Payments</Link>\n              <Link href=\"/profile\" className=\"text-gray-600 hover:text-purple-600\">Profile</Link>\n            </nav>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Breadcrumb */}\n        <nav className=\"flex mb-8\" aria-label=\"Breadcrumb\">\n          <ol className=\"flex items-center space-x-4\">\n            <li>\n              <Link href=\"/services\" className=\"text-gray-500 hover:text-gray-700\">Services</Link>\n            </li>\n            <li>\n              <svg className=\"flex-shrink-0 h-5 w-5 text-gray-400\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                <path fillRule=\"evenodd\" d=\"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\" clipRule=\"evenodd\" />\n              </svg>\n            </li>\n            <li>\n              <span className=\"text-gray-900 font-medium\">{service.name}</span>\n            </li>\n          </ol>\n        </nav>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n          {/* Service Details */}\n          <div className=\"lg:col-span-2\">\n            <div className=\"bg-white rounded-lg shadow\">\n              <div className=\"p-6\">\n                <div className=\"flex items-start space-x-4 mb-6\">\n                  <div className=\"text-4xl\">{service.icon}</div>\n                  <div className=\"flex-1\">\n                    <h1 className=\"text-2xl font-bold text-gray-900 mb-2\">{service.name}</h1>\n                    <div className=\"flex items-center space-x-4 text-sm text-gray-600 mb-4\">\n                      <span className=\"bg-purple-100 text-purple-800 px-2 py-1 rounded-full\">{service.platform}</span>\n                      <span className=\"bg-blue-100 text-blue-800 px-2 py-1 rounded-full\">{service.category}</span>\n                      <span className=\"bg-green-100 text-green-800 px-2 py-1 rounded-full\">{service.quality}</span>\n                    </div>\n                    <p className=\"text-gray-700 leading-relaxed\">{service.description}</p>\n                  </div>\n                </div>\n\n                {/* Service Stats */}\n                <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 mb-6\">\n                  <div className=\"text-center p-4 bg-gray-50 rounded-lg\">\n                    <p className=\"text-2xl font-bold text-purple-600\">${service.price}</p>\n                    <p className=\"text-sm text-gray-600\">per 1000</p>\n                  </div>\n                  <div className=\"text-center p-4 bg-gray-50 rounded-lg\">\n                    <p className=\"text-2xl font-bold text-green-600\">{service.successRate}%</p>\n                    <p className=\"text-sm text-gray-600\">Success Rate</p>\n                  </div>\n                  <div className=\"text-center p-4 bg-gray-50 rounded-lg\">\n                    <p className=\"text-2xl font-bold text-blue-600\">{service.totalOrders.toLocaleString()}</p>\n                    <p className=\"text-sm text-gray-600\">Total Orders</p>\n                  </div>\n                  <div className=\"text-center p-4 bg-gray-50 rounded-lg\">\n                    <p className=\"text-2xl font-bold text-orange-600\">{service.averageTime}</p>\n                    <p className=\"text-sm text-gray-600\">Avg. Time</p>\n                  </div>\n                </div>\n\n                {/* Features */}\n                <div className=\"mb-6\">\n                  <h3 className=\"text-lg font-medium text-gray-900 mb-3\">Features</h3>\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-2\">\n                    {service.features.map((feature, index) => (\n                      <div key={index} className=\"flex items-center space-x-2\">\n                        <svg className=\"w-5 h-5 text-green-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\n                        </svg>\n                        <span className=\"text-sm text-gray-700\">{feature}</span>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n\n                {/* Service Details */}\n                <div className=\"border-t pt-6\">\n                  <h3 className=\"text-lg font-medium text-gray-900 mb-3\">Service Details</h3>\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm\">\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-gray-600\">Min Order:</span>\n                      <span className=\"font-medium\">{service.minOrder.toLocaleString()}</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-gray-600\">Max Order:</span>\n                      <span className=\"font-medium\">{service.maxOrder.toLocaleString()}</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-gray-600\">Speed:</span>\n                      <span className=\"font-medium\">{service.speed}</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-gray-600\">Refill:</span>\n                      <span className=\"font-medium\">{service.refillEnabled ? `${service.refillPeriod} days` : 'No'}</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-gray-600\">Cancel:</span>\n                      <span className=\"font-medium\">{service.cancelEnabled ? 'Yes' : 'No'}</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-gray-600\">Link Required:</span>\n                      <span className=\"font-medium\">{service.requirements.linkRequired ? 'Yes' : 'No'}</span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Order Form */}\n          <div className=\"lg:col-span-1\">\n            <div className=\"bg-white rounded-lg shadow sticky top-8\">\n              <div className=\"p-6\">\n                <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Place Order</h3>\n                \n                <form onSubmit={handleOrderSubmit} className=\"space-y-4\">\n                  {/* Quantity */}\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Quantity</label>\n                    <input\n                      type=\"number\"\n                      value={quantity}\n                      onChange={(e) => handleQuantityChange(e.target.value)}\n                      min={service.minOrder}\n                      max={service.maxOrder}\n                      className=\"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-purple-500 focus:border-purple-500\"\n                    />\n                    <p className=\"text-xs text-gray-500 mt-1\">\n                      Min: {service.minOrder.toLocaleString()} - Max: {service.maxOrder.toLocaleString()}\n                    </p>\n                  </div>\n\n                  {/* Link */}\n                  {service.requirements.linkRequired && (\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Link</label>\n                      <input\n                        type=\"url\"\n                        value={link}\n                        onChange={(e) => setLink(e.target.value)}\n                        placeholder=\"https://instagram.com/username\"\n                        className=\"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-purple-500 focus:border-purple-500\"\n                        required\n                      />\n                    </div>\n                  )}\n\n                  {/* Custom Comments */}\n                  {service.requirements.customCommentsRequired && (\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Custom Comments</label>\n                      {customComments.map((comment, index) => (\n                        <div key={index} className=\"flex space-x-2 mb-2\">\n                          <input\n                            type=\"text\"\n                            value={comment}\n                            onChange={(e) => handleCommentChange(index, e.target.value)}\n                            placeholder={`Comment ${index + 1}`}\n                            className=\"flex-1 border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-purple-500 focus:border-purple-500\"\n                          />\n                          {customComments.length > 1 && (\n                            <button\n                              type=\"button\"\n                              onClick={() => handleRemoveComment(index)}\n                              className=\"px-2 py-2 text-red-600 hover:text-red-700\"\n                            >\n                              ×\n                            </button>\n                          )}\n                        </div>\n                      ))}\n                      {customComments.length < 10 && (\n                        <button\n                          type=\"button\"\n                          onClick={handleAddComment}\n                          className=\"text-sm text-purple-600 hover:text-purple-700\"\n                        >\n                          + Add Comment\n                        </button>\n                      )}\n                    </div>\n                  )}\n\n                  {/* Price Calculation */}\n                  <div className=\"bg-gray-50 rounded-lg p-4\">\n                    <div className=\"flex justify-between items-center mb-2\">\n                      <span className=\"text-sm text-gray-600\">Quantity:</span>\n                      <span className=\"font-medium\">{parseInt(quantity || '0').toLocaleString()}</span>\n                    </div>\n                    <div className=\"flex justify-between items-center mb-2\">\n                      <span className=\"text-sm text-gray-600\">Price per 1000:</span>\n                      <span className=\"font-medium\">${service.price}</span>\n                    </div>\n                    <div className=\"border-t pt-2\">\n                      <div className=\"flex justify-between items-center\">\n                        <span className=\"font-medium text-gray-900\">Total:</span>\n                        <span className=\"text-xl font-bold text-purple-600\">\n                          ${calculatePrice(parseInt(quantity || '0'))}\n                        </span>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Submit Button */}\n                  <button\n                    type=\"submit\"\n                    className=\"w-full bg-purple-600 text-white py-3 px-4 rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 font-medium\"\n                  >\n                    Place Order - ${calculatePrice(parseInt(quantity || '0'))}\n                  </button>\n                </form>\n\n                {/* Additional Info */}\n                <div className=\"mt-4 text-xs text-gray-500\">\n                  <p>• Orders are processed automatically</p>\n                  <p>• You will receive email notifications</p>\n                  <p>• Refill guarantee: {service.refillPeriod} days</p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAC;KAAG;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,2DAA2D;IAC3D,MAAM,UAAU;QACd,IAAI,OAAO,EAAE;QACb,MAAM;QACN,UAAU;QACV,UAAU;QACV,OAAO;QACP,WAAW;QACX,UAAU;QACV,UAAU;QACV,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;YACA;SACD;QACD,cAAc;YACZ,cAAc;YACd,kBAAkB;YAClB,wBAAwB;QAC1B;QACA,aAAa;QACb,OAAO;QACP,SAAS;QACT,eAAe;QACf,cAAc;QACd,eAAe;QACf,aAAa;QACb,aAAa;QACb,MAAM;IACR;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,QAAQ,KAAK,IAAI,CAAC,MAAM;QAC9B,OAAO,CAAC,QAAQ,QAAQ,KAAK,EAAE,OAAO,CAAC;IACzC;IAEA,MAAM,uBAAuB,CAAC;QAC5B,MAAM,WAAW,SAAS,UAAU;QACpC,IAAI,YAAY,QAAQ,QAAQ,IAAI,YAAY,QAAQ,QAAQ,EAAE;YAChE,YAAY;QACd;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI,eAAe,MAAM,GAAG,IAAI;YAC9B,kBAAkB;mBAAI;gBAAgB;aAAG;QAC3C;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,IAAI,eAAe,MAAM,GAAG,GAAG;YAC7B,kBAAkB,eAAe,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;QAC1D;IACF;IAEA,MAAM,sBAAsB,CAAC,OAAe;QAC1C,MAAM,cAAc;eAAI;SAAe;QACvC,WAAW,CAAC,MAAM,GAAG;QACrB,kBAAkB;IACpB;IAEA,MAAM,oBAAoB,CAAC;QACzB,EAAE,cAAc;QAChB,QAAQ,GAAG,CAAC,oBAAoB;YAC9B,WAAW,QAAQ,EAAE;YACrB,UAAU,SAAS;YACnB;YACA,gBAAgB,eAAe,MAAM,CAAC,CAAA,UAAW,QAAQ,IAAI,OAAO;QACtE;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;;sDACvB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;sDAEjD,8OAAC;4CAAK,WAAU;sDAAkC;;;;;;;;;;;;;;;;;0CAItD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAa,WAAU;kDAAsC;;;;;;kDACxE,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAY,WAAU;kDAA8B;;;;;;kDAC/D,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAU,WAAU;kDAAsC;;;;;;kDACrE,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAY,WAAU;kDAAsC;;;;;;kDACvE,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAsC;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAM9E,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;wBAAY,cAAW;kCACpC,cAAA,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC;8CACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAY,WAAU;kDAAoC;;;;;;;;;;;8CAEvE,8OAAC;8CACC,cAAA,8OAAC;wCAAI,WAAU;wCAAsC,MAAK;wCAAe,SAAQ;kDAC/E,cAAA,8OAAC;4CAAK,UAAS;4CAAU,GAAE;4CAAqH,UAAS;;;;;;;;;;;;;;;;8CAG7J,8OAAC;8CACC,cAAA,8OAAC;wCAAK,WAAU;kDAA6B,QAAQ,IAAI;;;;;;;;;;;;;;;;;;;;;;kCAK/D,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAAY,QAAQ,IAAI;;;;;;kEACvC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAAyC,QAAQ,IAAI;;;;;;0EACnE,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAwD,QAAQ,QAAQ;;;;;;kFACxF,8OAAC;wEAAK,WAAU;kFAAoD,QAAQ,QAAQ;;;;;;kFACpF,8OAAC;wEAAK,WAAU;kFAAsD,QAAQ,OAAO;;;;;;;;;;;;0EAEvF,8OAAC;gEAAE,WAAU;0EAAiC,QAAQ,WAAW;;;;;;;;;;;;;;;;;;0DAKrE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;;oEAAqC;oEAAE,QAAQ,KAAK;;;;;;;0EACjE,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;kEAEvC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;;oEAAqC,QAAQ,WAAW;oEAAC;;;;;;;0EACtE,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;kEAEvC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;0EAAoC,QAAQ,WAAW,CAAC,cAAc;;;;;;0EACnF,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;kEAEvC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;0EAAsC,QAAQ,WAAW;;;;;;0EACtE,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;0DAKzC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAyC;;;;;;kEACvD,8OAAC;wDAAI,WAAU;kEACZ,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC9B,8OAAC;gEAAgB,WAAU;;kFACzB,8OAAC;wEAAI,WAAU;wEAAyB,MAAK;wEAAO,QAAO;wEAAe,SAAQ;kFAChF,cAAA,8OAAC;4EAAK,eAAc;4EAAQ,gBAAe;4EAAQ,aAAa;4EAAG,GAAE;;;;;;;;;;;kFAEvE,8OAAC;wEAAK,WAAU;kFAAyB;;;;;;;+DAJjC;;;;;;;;;;;;;;;;0DAWhB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAyC;;;;;;kEACvD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAgB;;;;;;kFAChC,8OAAC;wEAAK,WAAU;kFAAe,QAAQ,QAAQ,CAAC,cAAc;;;;;;;;;;;;0EAEhE,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAgB;;;;;;kFAChC,8OAAC;wEAAK,WAAU;kFAAe,QAAQ,QAAQ,CAAC,cAAc;;;;;;;;;;;;0EAEhE,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAgB;;;;;;kFAChC,8OAAC;wEAAK,WAAU;kFAAe,QAAQ,KAAK;;;;;;;;;;;;0EAE9C,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAgB;;;;;;kFAChC,8OAAC;wEAAK,WAAU;kFAAe,QAAQ,aAAa,GAAG,GAAG,QAAQ,YAAY,CAAC,KAAK,CAAC,GAAG;;;;;;;;;;;;0EAE1F,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAgB;;;;;;kFAChC,8OAAC;wEAAK,WAAU;kFAAe,QAAQ,aAAa,GAAG,QAAQ;;;;;;;;;;;;0EAEjE,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAgB;;;;;;kFAChC,8OAAC;wEAAK,WAAU;kFAAe,QAAQ,YAAY,CAAC,YAAY,GAAG,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CASvF,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAyC;;;;;;0DAEvD,8OAAC;gDAAK,UAAU;gDAAmB,WAAU;;kEAE3C,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAChE,8OAAC;gEACC,MAAK;gEACL,OAAO;gEACP,UAAU,CAAC,IAAM,qBAAqB,EAAE,MAAM,CAAC,KAAK;gEACpD,KAAK,QAAQ,QAAQ;gEACrB,KAAK,QAAQ,QAAQ;gEACrB,WAAU;;;;;;0EAEZ,8OAAC;gEAAE,WAAU;;oEAA6B;oEAClC,QAAQ,QAAQ,CAAC,cAAc;oEAAG;oEAAS,QAAQ,QAAQ,CAAC,cAAc;;;;;;;;;;;;;oDAKnF,QAAQ,YAAY,CAAC,YAAY,kBAChC,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAChE,8OAAC;gEACC,MAAK;gEACL,OAAO;gEACP,UAAU,CAAC,IAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;gEACvC,aAAY;gEACZ,WAAU;gEACV,QAAQ;;;;;;;;;;;;oDAMb,QAAQ,YAAY,CAAC,sBAAsB,kBAC1C,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;4DAC/D,eAAe,GAAG,CAAC,CAAC,SAAS,sBAC5B,8OAAC;oEAAgB,WAAU;;sFACzB,8OAAC;4EACC,MAAK;4EACL,OAAO;4EACP,UAAU,CAAC,IAAM,oBAAoB,OAAO,EAAE,MAAM,CAAC,KAAK;4EAC1D,aAAa,CAAC,QAAQ,EAAE,QAAQ,GAAG;4EACnC,WAAU;;;;;;wEAEX,eAAe,MAAM,GAAG,mBACvB,8OAAC;4EACC,MAAK;4EACL,SAAS,IAAM,oBAAoB;4EACnC,WAAU;sFACX;;;;;;;mEAbK;;;;;4DAmBX,eAAe,MAAM,GAAG,oBACvB,8OAAC;gEACC,MAAK;gEACL,SAAS;gEACT,WAAU;0EACX;;;;;;;;;;;;kEAQP,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAwB;;;;;;kFACxC,8OAAC;wEAAK,WAAU;kFAAe,SAAS,YAAY,KAAK,cAAc;;;;;;;;;;;;0EAEzE,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAwB;;;;;;kFACxC,8OAAC;wEAAK,WAAU;;4EAAc;4EAAE,QAAQ,KAAK;;;;;;;;;;;;;0EAE/C,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFAA4B;;;;;;sFAC5C,8OAAC;4EAAK,WAAU;;gFAAoC;gFAChD,eAAe,SAAS,YAAY;;;;;;;;;;;;;;;;;;;;;;;;kEAO9C,8OAAC;wDACC,MAAK;wDACL,WAAU;;4DACX;4DACiB,eAAe,SAAS,YAAY;;;;;;;;;;;;;0DAKxD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;kEAAE;;;;;;kEACH,8OAAC;kEAAE;;;;;;kEACH,8OAAC;;4DAAE;4DAAqB,QAAQ,YAAY;4DAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS/D", "debugId": null}}]}