'use client';

import { useState } from 'react';
import Link from 'next/link';

export default function NewOrder() {
  const [selectedPlatform, setSelectedPlatform] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [filteredServices, setFilteredServices] = useState([]);

  const platforms = [
    { id: 'instagram', name: 'Instagram', icon: '📷', color: 'bg-pink-500' },
    { id: 'tiktok', name: 'TikTok', icon: '🎵', color: 'bg-black' },
    { id: 'youtube', name: 'YouTube', icon: '📺', color: 'bg-red-500' },
    { id: 'facebook', name: 'Facebook', icon: '📘', color: 'bg-blue-500' },
    { id: 'twitter', name: 'Twitter', icon: '🐦', color: 'bg-blue-400' },
    { id: 'snapchat', name: 'Snapchat', icon: '👻', color: 'bg-yellow-400' }
  ];

  const categories = [
    { id: 'followers', name: 'Followers', icon: '👥' },
    { id: 'likes', name: 'Likes', icon: '❤️' },
    { id: 'views', name: 'Views', icon: '👁️' },
    { id: 'comments', name: 'Comments', icon: '💬' },
    { id: 'shares', name: 'Shares', icon: '🔄' },
    { id: 'subscribers', name: 'Subscribers', icon: '🔔' }
  ];

  const mockServices = [
    {
      id: 1,
      name: 'Instagram Followers - High Quality',
      platform: 'instagram',
      category: 'followers',
      price: 2.50,
      minOrder: 100,
      maxOrder: 50000,
      quality: 'High Quality',
      speed: 'Fast',
      description: 'Real and active Instagram followers'
    },
    {
      id: 2,
      name: 'Instagram Likes - Premium',
      platform: 'instagram',
      category: 'likes',
      price: 1.20,
      minOrder: 50,
      maxOrder: 100000,
      quality: 'Premium',
      speed: 'Instant',
      description: 'High-quality Instagram likes from real users'
    },
    {
      id: 3,
      name: 'TikTok Views - Viral',
      platform: 'tiktok',
      category: 'views',
      price: 0.80,
      minOrder: 1000,
      maxOrder: 1000000,
      quality: 'High Quality',
      speed: 'Fast',
      description: 'Boost your TikTok video views'
    },
    {
      id: 4,
      name: 'YouTube Subscribers - Real',
      platform: 'youtube',
      category: 'subscribers',
      price: 4.50,
      minOrder: 50,
      maxOrder: 10000,
      quality: 'Premium',
      speed: 'Medium',
      description: 'Real YouTube subscribers with profile pictures'
    }
  ];

  const handlePlatformSelect = (platformId: string) => {
    setSelectedPlatform(platformId);
    setSelectedCategory('');
    updateFilteredServices(platformId, '');
  };

  const handleCategorySelect = (categoryId: string) => {
    setSelectedCategory(categoryId);
    updateFilteredServices(selectedPlatform, categoryId);
  };

  const updateFilteredServices = (platform: string, category: string) => {
    let filtered = mockServices;
    
    if (platform) {
      filtered = filtered.filter(service => service.platform === platform);
    }
    
    if (category) {
      filtered = filtered.filter(service => service.category === category);
    }
    
    setFilteredServices(filtered as any);
  };

  const getPlatformIcon = (platformId: string) => {
    const platform = platforms.find(p => p.id === platformId);
    return platform ? platform.icon : '📱';
  };

  const getCategoryIcon = (categoryId: string) => {
    const category = categories.find(c => c.id === categoryId);
    return category ? category.icon : '⭐';
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <Link href="/" className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-sm">T</span>
                </div>
                <span className="text-xl font-bold text-gray-900">Trendz</span>
              </Link>
            </div>
            
            <nav className="hidden md:flex space-x-8">
              <Link href="/dashboard" className="text-gray-600 hover:text-purple-600">Dashboard</Link>
              <Link href="/services" className="text-gray-600 hover:text-purple-600">Services</Link>
              <Link href="/orders" className="text-gray-600 hover:text-purple-600">Orders</Link>
              <Link href="/payments" className="text-gray-600 hover:text-purple-600">Payments</Link>
              <Link href="/profile" className="text-gray-600 hover:text-purple-600">Profile</Link>
            </nav>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-2xl font-bold text-gray-900">New Order</h1>
          <p className="text-gray-600">Choose your platform and service to get started</p>
        </div>

        {/* Step 1: Platform Selection */}
        <div className="mb-8">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Step 1: Choose Platform</h2>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            {platforms.map((platform) => (
              <button
                key={platform.id}
                onClick={() => handlePlatformSelect(platform.id)}
                className={`p-4 rounded-lg border-2 transition-all ${
                  selectedPlatform === platform.id
                    ? 'border-purple-500 bg-purple-50'
                    : 'border-gray-200 bg-white hover:border-gray-300'
                }`}
              >
                <div className={`w-12 h-12 ${platform.color} rounded-lg flex items-center justify-center mx-auto mb-2`}>
                  <span className="text-2xl">{platform.icon}</span>
                </div>
                <p className="text-sm font-medium text-gray-900">{platform.name}</p>
              </button>
            ))}
          </div>
        </div>

        {/* Step 2: Category Selection */}
        {selectedPlatform && (
          <div className="mb-8">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Step 2: Choose Service Type</h2>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
              {categories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => handleCategorySelect(category.id)}
                  className={`p-4 rounded-lg border-2 transition-all ${
                    selectedCategory === category.id
                      ? 'border-purple-500 bg-purple-50'
                      : 'border-gray-200 bg-white hover:border-gray-300'
                  }`}
                >
                  <div className="text-2xl mb-2">{category.icon}</div>
                  <p className="text-sm font-medium text-gray-900">{category.name}</p>
                </button>
              ))}
            </div>
          </div>
        )}

        {/* Step 3: Service Selection */}
        {selectedPlatform && selectedCategory && (
          <div className="mb-8">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Step 3: Choose Service</h2>
            {filteredServices.length === 0 ? (
              <div className="bg-white rounded-lg shadow p-8 text-center">
                <div className="text-4xl mb-4">🔍</div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">No Services Found</h3>
                <p className="text-gray-600 mb-4">
                  We don't have any services for {selectedCategory} on {selectedPlatform} yet.
                </p>
                <Link href="/services" className="text-purple-600 hover:text-purple-500 font-medium">
                  Browse All Services
                </Link>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredServices.map((service: any) => (
                  <div key={service.id} className="bg-white rounded-lg shadow hover:shadow-md transition-shadow">
                    <div className="p-6">
                      <div className="flex items-center mb-4">
                        <span className="text-2xl mr-3">{getPlatformIcon(service.platform)}</span>
                        <div>
                          <h3 className="font-semibold text-gray-900">{service.name}</h3>
                          <div className="flex items-center space-x-2 mt-1">
                            <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
                              {service.quality}
                            </span>
                            <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                              {service.speed}
                            </span>
                          </div>
                        </div>
                      </div>
                      
                      <p className="text-sm text-gray-600 mb-4">{service.description}</p>
                      
                      <div className="space-y-2 text-sm mb-4">
                        <div className="flex justify-between">
                          <span className="text-gray-600">Price:</span>
                          <span className="font-medium">${service.price} / 1000</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Min Order:</span>
                          <span className="font-medium">{service.minOrder.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Max Order:</span>
                          <span className="font-medium">{service.maxOrder.toLocaleString()}</span>
                        </div>
                      </div>
                      
                      <Link
                        href={`/services/${service.id}`}
                        className="block w-full bg-purple-600 text-white text-center py-2 px-4 rounded-md hover:bg-purple-700 transition-colors font-medium"
                      >
                        Select Service
                      </Link>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Quick Start */}
        {!selectedPlatform && (
          <div className="bg-white rounded-lg shadow p-8">
            <div className="text-center">
              <div className="text-4xl mb-4">🚀</div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">Quick Start Guide</h3>
              <p className="text-gray-600 mb-6">
                Follow these simple steps to place your first order:
              </p>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-left">
                <div className="flex items-start space-x-3">
                  <div className="w-8 h-8 bg-purple-600 text-white rounded-full flex items-center justify-center text-sm font-bold">
                    1
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900">Choose Platform</h4>
                    <p className="text-sm text-gray-600">Select the social media platform you want to boost</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-8 h-8 bg-purple-600 text-white rounded-full flex items-center justify-center text-sm font-bold">
                    2
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900">Pick Service</h4>
                    <p className="text-sm text-gray-600">Choose the type of engagement you need</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-8 h-8 bg-purple-600 text-white rounded-full flex items-center justify-center text-sm font-bold">
                    3
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900">Place Order</h4>
                    <p className="text-sm text-gray-600">Enter details and complete your purchase</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
