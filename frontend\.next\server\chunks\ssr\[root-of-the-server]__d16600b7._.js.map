{"version": 3, "sources": [], "sections": [{"offset": {"line": 141, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/trendz/frontend/src/lib/api.ts"], "sourcesContent": ["import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';\nimport Cookies from 'js-cookie';\nimport toast from 'react-hot-toast';\n\n// Types\nimport type { \n  ApiResponse, \n  User, \n  Service, \n  Order, \n  Payment, \n  LoginForm, \n  RegisterForm, \n  OrderForm, \n  PaymentForm,\n  PaginatedResponse,\n  PaginationParams\n} from '@/types';\n\nclass ApiClient {\n  private client: AxiosInstance;\n  private baseURL: string;\n\n  constructor() {\n    this.baseURL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';\n    \n    this.client = axios.create({\n      baseURL: this.baseURL,\n      timeout: 30000,\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    });\n\n    this.setupInterceptors();\n  }\n\n  private setupInterceptors() {\n    // Request interceptor\n    this.client.interceptors.request.use(\n      (config) => {\n        const token = Cookies.get('token');\n        if (token) {\n          config.headers.Authorization = `Bearer ${token}`;\n        }\n        \n        // Add language header\n        const language = Cookies.get('i18next') || 'en';\n        config.headers['Accept-Language'] = language;\n        \n        return config;\n      },\n      (error) => {\n        return Promise.reject(error);\n      }\n    );\n\n    // Response interceptor\n    this.client.interceptors.response.use(\n      (response: AxiosResponse) => {\n        return response;\n      },\n      (error) => {\n        if (error.response?.status === 401) {\n          // Unauthorized - clear token and redirect to login\n          Cookies.remove('token');\n          if (typeof window !== 'undefined') {\n            window.location.href = '/auth/login';\n          }\n        } else if (error.response?.status >= 500) {\n          // Server error\n          toast.error('Server error. Please try again later.');\n        } else if (error.code === 'ECONNABORTED') {\n          // Timeout\n          toast.error('Request timeout. Please try again.');\n        } else if (!error.response) {\n          // Network error\n          toast.error('Network error. Please check your connection.');\n        }\n        \n        return Promise.reject(error);\n      }\n    );\n  }\n\n  // Generic request method\n  private async request<T>(config: AxiosRequestConfig): Promise<ApiResponse<T>> {\n    try {\n      const response = await this.client.request<ApiResponse<T>>(config);\n      return response.data;\n    } catch (error: any) {\n      const errorMessage = error.response?.data?.error || error.message || 'An error occurred';\n      throw new Error(errorMessage);\n    }\n  }\n\n  // Auth endpoints\n  async login(credentials: LoginForm): Promise<ApiResponse<{ token: string; user: User }>> {\n    return this.request({\n      method: 'POST',\n      url: '/auth/login',\n      data: credentials,\n    });\n  }\n\n  async register(data: RegisterForm): Promise<ApiResponse<{ token: string; user: User }>> {\n    return this.request({\n      method: 'POST',\n      url: '/auth/register',\n      data,\n    });\n  }\n\n  async logout(): Promise<ApiResponse> {\n    return this.request({\n      method: 'POST',\n      url: '/auth/logout',\n    });\n  }\n\n  async getMe(): Promise<ApiResponse<{ user: User }>> {\n    return this.request({\n      method: 'GET',\n      url: '/auth/me',\n    });\n  }\n\n  async updateProfile(data: Partial<User>): Promise<ApiResponse<{ user: User }>> {\n    return this.request({\n      method: 'PUT',\n      url: '/auth/profile',\n      data,\n    });\n  }\n\n  async changePassword(data: { currentPassword: string; newPassword: string }): Promise<ApiResponse> {\n    return this.request({\n      method: 'PUT',\n      url: '/auth/change-password',\n      data,\n    });\n  }\n\n  async forgotPassword(email: string): Promise<ApiResponse> {\n    return this.request({\n      method: 'POST',\n      url: '/auth/forgot-password',\n      data: { email },\n    });\n  }\n\n  async resetPassword(data: { token: string; password: string }): Promise<ApiResponse> {\n    return this.request({\n      method: 'POST',\n      url: '/auth/reset-password',\n      data,\n    });\n  }\n\n  // Services endpoints\n  async getServices(params?: PaginationParams & {\n    platform?: string;\n    category?: string;\n    featured?: boolean;\n    q?: string;\n  }): Promise<ApiResponse<PaginatedResponse<Service>>> {\n    return this.request({\n      method: 'GET',\n      url: '/services',\n      params,\n    });\n  }\n\n  async getService(id: string): Promise<ApiResponse<{ service: Service }>> {\n    return this.request({\n      method: 'GET',\n      url: `/services/${id}`,\n    });\n  }\n\n  async calculatePrice(data: { serviceId: string; quantity: number }): Promise<ApiResponse<{ price: number }>> {\n    return this.request({\n      method: 'POST',\n      url: '/services/calculate-price',\n      data,\n    });\n  }\n\n  // Orders endpoints\n  async createOrder(data: OrderForm): Promise<ApiResponse<{ order: Order }>> {\n    return this.request({\n      method: 'POST',\n      url: '/orders',\n      data,\n    });\n  }\n\n  async getOrders(params?: PaginationParams & {\n    status?: string;\n    serviceId?: string;\n    platform?: string;\n    startDate?: string;\n    endDate?: string;\n  }): Promise<ApiResponse<PaginatedResponse<Order>>> {\n    return this.request({\n      method: 'GET',\n      url: '/orders',\n      params,\n    });\n  }\n\n  async getOrder(id: string): Promise<ApiResponse<{ order: Order }>> {\n    return this.request({\n      method: 'GET',\n      url: `/orders/${id}`,\n    });\n  }\n\n  async cancelOrder(id: string, reason?: string): Promise<ApiResponse> {\n    return this.request({\n      method: 'PATCH',\n      url: `/orders/${id}/cancel`,\n      data: { reason },\n    });\n  }\n\n  async requestRefill(id: string, reason?: string): Promise<ApiResponse> {\n    return this.request({\n      method: 'PATCH',\n      url: `/orders/${id}/refill`,\n      data: { reason },\n    });\n  }\n\n  async getOrderStatistics(params?: { startDate?: string; endDate?: string }): Promise<ApiResponse<any>> {\n    return this.request({\n      method: 'GET',\n      url: '/orders/statistics',\n      params,\n    });\n  }\n\n  // Payments endpoints\n  async createPayment(data: PaymentForm): Promise<ApiResponse<{ payment: Payment }>> {\n    return this.request({\n      method: 'POST',\n      url: '/payments',\n      data,\n    });\n  }\n\n  async getPayments(params?: PaginationParams & {\n    status?: string;\n    method?: string;\n    type?: string;\n    startDate?: string;\n    endDate?: string;\n  }): Promise<ApiResponse<PaginatedResponse<Payment>>> {\n    return this.request({\n      method: 'GET',\n      url: '/payments',\n      params,\n    });\n  }\n\n  async getPayment(id: string): Promise<ApiResponse<{ payment: Payment }>> {\n    return this.request({\n      method: 'GET',\n      url: `/payments/${id}`,\n    });\n  }\n\n  async confirmManualPayment(id: string, details: any): Promise<ApiResponse> {\n    return this.request({\n      method: 'PATCH',\n      url: `/payments/${id}/confirm`,\n      data: details,\n    });\n  }\n\n  async getPaymentMethods(): Promise<ApiResponse<any>> {\n    return this.request({\n      method: 'GET',\n      url: '/payments/methods',\n    });\n  }\n\n  async getPaymentStatistics(params?: { startDate?: string; endDate?: string }): Promise<ApiResponse<any>> {\n    return this.request({\n      method: 'GET',\n      url: '/payments/statistics',\n      params,\n    });\n  }\n\n  // PayPal endpoints\n  async createPaypalOrder(data: { amount: number; currency?: string }): Promise<ApiResponse<{ orderID: string }>> {\n    return this.request({\n      method: 'POST',\n      url: '/payments/paypal/create-order',\n      data,\n    });\n  }\n\n  async capturePaypalOrder(orderID: string): Promise<ApiResponse> {\n    return this.request({\n      method: 'POST',\n      url: `/payments/paypal/capture-order/${orderID}`,\n    });\n  }\n\n  // User endpoints\n  async getUserBalance(): Promise<ApiResponse<{ balance: number }>> {\n    return this.request({\n      method: 'GET',\n      url: '/users/balance',\n    });\n  }\n\n  async getUserOrders(params?: PaginationParams): Promise<ApiResponse<PaginatedResponse<Order>>> {\n    return this.request({\n      method: 'GET',\n      url: '/users/orders',\n      params,\n    });\n  }\n\n  async getUserPayments(params?: PaginationParams): Promise<ApiResponse<PaginatedResponse<Payment>>> {\n    return this.request({\n      method: 'GET',\n      url: '/users/payments',\n      params,\n    });\n  }\n\n  async getUserStatistics(params?: { startDate?: string; endDate?: string }): Promise<ApiResponse<any>> {\n    return this.request({\n      method: 'GET',\n      url: '/users/statistics',\n      params,\n    });\n  }\n\n  async updateNotificationSettings(settings: any): Promise<ApiResponse> {\n    return this.request({\n      method: 'PUT',\n      url: '/users/notifications',\n      data: settings,\n    });\n  }\n\n  async uploadAvatar(file: File): Promise<ApiResponse<{ avatar: string }>> {\n    const formData = new FormData();\n    formData.append('avatar', file);\n\n    return this.request({\n      method: 'POST',\n      url: '/users/avatar',\n      data: formData,\n      headers: {\n        'Content-Type': 'multipart/form-data',\n      },\n    });\n  }\n\n  async deleteAvatar(): Promise<ApiResponse> {\n    return this.request({\n      method: 'DELETE',\n      url: '/users/avatar',\n    });\n  }\n}\n\n// Create and export a singleton instance\nconst apiClient = new ApiClient();\nexport default apiClient;\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAiBA,MAAM;IACI,OAAsB;IACtB,QAAgB;IAExB,aAAc;QACZ,IAAI,CAAC,OAAO,GAAG,iEAAmC;QAElD,IAAI,CAAC,MAAM,GAAG,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;YACzB,SAAS,IAAI,CAAC,OAAO;YACrB,SAAS;YACT,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,IAAI,CAAC,iBAAiB;IACxB;IAEQ,oBAAoB;QAC1B,sBAAsB;QACtB,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CAClC,CAAC;YACC,MAAM,QAAQ,qJAAA,CAAA,UAAO,CAAC,GAAG,CAAC;YAC1B,IAAI,OAAO;gBACT,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;YAClD;YAEA,sBAAsB;YACtB,MAAM,WAAW,qJAAA,CAAA,UAAO,CAAC,GAAG,CAAC,cAAc;YAC3C,OAAO,OAAO,CAAC,kBAAkB,GAAG;YAEpC,OAAO;QACT,GACA,CAAC;YACC,OAAO,QAAQ,MAAM,CAAC;QACxB;QAGF,uBAAuB;QACvB,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CACnC,CAAC;YACC,OAAO;QACT,GACA,CAAC;YACC,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;gBAClC,mDAAmD;gBACnD,qJAAA,CAAA,UAAO,CAAC,MAAM,CAAC;gBACf;;YAGF,OAAO,IAAI,MAAM,QAAQ,EAAE,UAAU,KAAK;gBACxC,eAAe;gBACf,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACd,OAAO,IAAI,MAAM,IAAI,KAAK,gBAAgB;gBACxC,UAAU;gBACV,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACd,OAAO,IAAI,CAAC,MAAM,QAAQ,EAAE;gBAC1B,gBAAgB;gBAChB,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACd;YAEA,OAAO,QAAQ,MAAM,CAAC;QACxB;IAEJ;IAEA,yBAAyB;IACzB,MAAc,QAAW,MAA0B,EAA2B;QAC5E,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAiB;YAC3D,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAY;YACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,SAAS,MAAM,OAAO,IAAI;YACrE,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,iBAAiB;IACjB,MAAM,MAAM,WAAsB,EAAuD;QACvF,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,QAAQ;YACR,KAAK;YACL,MAAM;QACR;IACF;IAEA,MAAM,SAAS,IAAkB,EAAuD;QACtF,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,QAAQ;YACR,KAAK;YACL;QACF;IACF;IAEA,MAAM,SAA+B;QACnC,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,QAAQ;YACR,KAAK;QACP;IACF;IAEA,MAAM,QAA8C;QAClD,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,QAAQ;YACR,KAAK;QACP;IACF;IAEA,MAAM,cAAc,IAAmB,EAAwC;QAC7E,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,QAAQ;YACR,KAAK;YACL;QACF;IACF;IAEA,MAAM,eAAe,IAAsD,EAAwB;QACjG,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,QAAQ;YACR,KAAK;YACL;QACF;IACF;IAEA,MAAM,eAAe,KAAa,EAAwB;QACxD,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,QAAQ;YACR,KAAK;YACL,MAAM;gBAAE;YAAM;QAChB;IACF;IAEA,MAAM,cAAc,IAAyC,EAAwB;QACnF,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,QAAQ;YACR,KAAK;YACL;QACF;IACF;IAEA,qBAAqB;IACrB,MAAM,YAAY,MAKjB,EAAoD;QACnD,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,QAAQ;YACR,KAAK;YACL;QACF;IACF;IAEA,MAAM,WAAW,EAAU,EAA8C;QACvE,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,QAAQ;YACR,KAAK,CAAC,UAAU,EAAE,IAAI;QACxB;IACF;IAEA,MAAM,eAAe,IAA6C,EAA2C;QAC3G,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,QAAQ;YACR,KAAK;YACL;QACF;IACF;IAEA,mBAAmB;IACnB,MAAM,YAAY,IAAe,EAA0C;QACzE,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,QAAQ;YACR,KAAK;YACL;QACF;IACF;IAEA,MAAM,UAAU,MAMf,EAAkD;QACjD,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,QAAQ;YACR,KAAK;YACL;QACF;IACF;IAEA,MAAM,SAAS,EAAU,EAA0C;QACjE,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,QAAQ;YACR,KAAK,CAAC,QAAQ,EAAE,IAAI;QACtB;IACF;IAEA,MAAM,YAAY,EAAU,EAAE,MAAe,EAAwB;QACnE,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,QAAQ;YACR,KAAK,CAAC,QAAQ,EAAE,GAAG,OAAO,CAAC;YAC3B,MAAM;gBAAE;YAAO;QACjB;IACF;IAEA,MAAM,cAAc,EAAU,EAAE,MAAe,EAAwB;QACrE,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,QAAQ;YACR,KAAK,CAAC,QAAQ,EAAE,GAAG,OAAO,CAAC;YAC3B,MAAM;gBAAE;YAAO;QACjB;IACF;IAEA,MAAM,mBAAmB,MAAiD,EAA6B;QACrG,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,QAAQ;YACR,KAAK;YACL;QACF;IACF;IAEA,qBAAqB;IACrB,MAAM,cAAc,IAAiB,EAA8C;QACjF,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,QAAQ;YACR,KAAK;YACL;QACF;IACF;IAEA,MAAM,YAAY,MAMjB,EAAoD;QACnD,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,QAAQ;YACR,KAAK;YACL;QACF;IACF;IAEA,MAAM,WAAW,EAAU,EAA8C;QACvE,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,QAAQ;YACR,KAAK,CAAC,UAAU,EAAE,IAAI;QACxB;IACF;IAEA,MAAM,qBAAqB,EAAU,EAAE,OAAY,EAAwB;QACzE,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,QAAQ;YACR,KAAK,CAAC,UAAU,EAAE,GAAG,QAAQ,CAAC;YAC9B,MAAM;QACR;IACF;IAEA,MAAM,oBAA+C;QACnD,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,QAAQ;YACR,KAAK;QACP;IACF;IAEA,MAAM,qBAAqB,MAAiD,EAA6B;QACvG,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,QAAQ;YACR,KAAK;YACL;QACF;IACF;IAEA,mBAAmB;IACnB,MAAM,kBAAkB,IAA2C,EAA6C;QAC9G,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,QAAQ;YACR,KAAK;YACL;QACF;IACF;IAEA,MAAM,mBAAmB,OAAe,EAAwB;QAC9D,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,QAAQ;YACR,KAAK,CAAC,+BAA+B,EAAE,SAAS;QAClD;IACF;IAEA,iBAAiB;IACjB,MAAM,iBAA4D;QAChE,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,QAAQ;YACR,KAAK;QACP;IACF;IAEA,MAAM,cAAc,MAAyB,EAAkD;QAC7F,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,QAAQ;YACR,KAAK;YACL;QACF;IACF;IAEA,MAAM,gBAAgB,MAAyB,EAAoD;QACjG,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,QAAQ;YACR,KAAK;YACL;QACF;IACF;IAEA,MAAM,kBAAkB,MAAiD,EAA6B;QACpG,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,QAAQ;YACR,KAAK;YACL;QACF;IACF;IAEA,MAAM,2BAA2B,QAAa,EAAwB;QACpE,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,QAAQ;YACR,KAAK;YACL,MAAM;QACR;IACF;IAEA,MAAM,aAAa,IAAU,EAA4C;QACvE,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,UAAU;QAE1B,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,QAAQ;YACR,KAAK;YACL,MAAM;YACN,SAAS;gBACP,gBAAgB;YAClB;QACF;IACF;IAEA,MAAM,eAAqC;QACzC,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,QAAQ;YACR,KAAK;QACP;IACF;AACF;AAEA,yCAAyC;AACzC,MAAM,YAAY,IAAI;uCACP", "debugId": null}}, {"offset": {"line": 450, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/trendz/frontend/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';\nimport { useRouter } from 'next/navigation';\nimport Cookies from 'js-cookie';\nimport toast from 'react-hot-toast';\nimport apiClient from '@/lib/api';\nimport type { User, LoginForm, RegisterForm, AuthContextType } from '@/types';\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\ninterface AuthProviderProps {\n  children: ReactNode;\n}\n\nexport const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {\n  const [user, setUser] = useState<User | null>(null);\n  const [token, setToken] = useState<string | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const router = useRouter();\n\n  // Initialize auth state\n  useEffect(() => {\n    const initAuth = async () => {\n      const savedToken = Cookies.get('token');\n      \n      if (savedToken) {\n        setToken(savedToken);\n        try {\n          const response = await apiClient.getMe();\n          if (response.success && response.data) {\n            setUser(response.data.user);\n          } else {\n            // Invalid token, clear it\n            Cookies.remove('token');\n            setToken(null);\n          }\n        } catch (error) {\n          console.error('Failed to get user info:', error);\n          Cookies.remove('token');\n          setToken(null);\n        }\n      }\n      \n      setIsLoading(false);\n    };\n\n    initAuth();\n  }, []);\n\n  const login = async (credentials: LoginForm) => {\n    try {\n      setIsLoading(true);\n      const response = await apiClient.login(credentials);\n      \n      if (response.success && response.data) {\n        const { token: newToken, user: userData } = response.data;\n        \n        // Save token to cookies\n        Cookies.set('token', newToken, { \n          expires: 7, // 7 days\n          secure: process.env.NODE_ENV === 'production',\n          sameSite: 'strict'\n        });\n        \n        setToken(newToken);\n        setUser(userData);\n        \n        toast.success('Login successful!');\n        \n        // Redirect based on user role\n        if (userData.role === 'admin') {\n          router.push('/admin');\n        } else {\n          router.push('/dashboard');\n        }\n      } else {\n        throw new Error(response.error || 'Login failed');\n      }\n    } catch (error: any) {\n      console.error('Login error:', error);\n      toast.error(error.message || 'Login failed');\n      throw error;\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const register = async (data: RegisterForm) => {\n    try {\n      setIsLoading(true);\n      const response = await apiClient.register(data);\n      \n      if (response.success && response.data) {\n        const { token: newToken, user: userData } = response.data;\n        \n        // Save token to cookies\n        Cookies.set('token', newToken, { \n          expires: 7, // 7 days\n          secure: process.env.NODE_ENV === 'production',\n          sameSite: 'strict'\n        });\n        \n        setToken(newToken);\n        setUser(userData);\n        \n        toast.success('Registration successful!');\n        router.push('/dashboard');\n      } else {\n        throw new Error(response.error || 'Registration failed');\n      }\n    } catch (error: any) {\n      console.error('Registration error:', error);\n      toast.error(error.message || 'Registration failed');\n      throw error;\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const logout = async () => {\n    try {\n      // Call logout endpoint\n      await apiClient.logout();\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      // Clear local state regardless of API call result\n      Cookies.remove('token');\n      setToken(null);\n      setUser(null);\n      toast.success('Logged out successfully');\n      router.push('/');\n    }\n  };\n\n  const updateProfile = async (data: Partial<User>) => {\n    try {\n      const response = await apiClient.updateProfile(data);\n      \n      if (response.success && response.data) {\n        setUser(response.data.user);\n        toast.success('Profile updated successfully');\n      } else {\n        throw new Error(response.error || 'Profile update failed');\n      }\n    } catch (error: any) {\n      console.error('Profile update error:', error);\n      toast.error(error.message || 'Profile update failed');\n      throw error;\n    }\n  };\n\n  const value: AuthContextType = {\n    user,\n    token,\n    isLoading,\n    isAuthenticated: !!user && !!token,\n    login,\n    register,\n    logout,\n    updateProfile,\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n\nexport const useAuth = (): AuthContextType => {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport default AuthContext;\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AASA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAMxD,MAAM,eAA4C,CAAC,EAAE,QAAQ,EAAE;IACpE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,wBAAwB;IACxB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW;YACf,MAAM,aAAa,qJAAA,CAAA,UAAO,CAAC,GAAG,CAAC;YAE/B,IAAI,YAAY;gBACd,SAAS;gBACT,IAAI;oBACF,MAAM,WAAW,MAAM,iHAAA,CAAA,UAAS,CAAC,KAAK;oBACtC,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;wBACrC,QAAQ,SAAS,IAAI,CAAC,IAAI;oBAC5B,OAAO;wBACL,0BAA0B;wBAC1B,qJAAA,CAAA,UAAO,CAAC,MAAM,CAAC;wBACf,SAAS;oBACX;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,4BAA4B;oBAC1C,qJAAA,CAAA,UAAO,CAAC,MAAM,CAAC;oBACf,SAAS;gBACX;YACF;YAEA,aAAa;QACf;QAEA;IACF,GAAG,EAAE;IAEL,MAAM,QAAQ,OAAO;QACnB,IAAI;YACF,aAAa;YACb,MAAM,WAAW,MAAM,iHAAA,CAAA,UAAS,CAAC,KAAK,CAAC;YAEvC,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,MAAM,EAAE,OAAO,QAAQ,EAAE,MAAM,QAAQ,EAAE,GAAG,SAAS,IAAI;gBAEzD,wBAAwB;gBACxB,qJAAA,CAAA,UAAO,CAAC,GAAG,CAAC,SAAS,UAAU;oBAC7B,SAAS;oBACT,QAAQ,oDAAyB;oBACjC,UAAU;gBACZ;gBAEA,SAAS;gBACT,QAAQ;gBAER,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;gBAEd,8BAA8B;gBAC9B,IAAI,SAAS,IAAI,KAAK,SAAS;oBAC7B,OAAO,IAAI,CAAC;gBACd,OAAO;oBACL,OAAO,IAAI,CAAC;gBACd;YACF,OAAO;gBACL,MAAM,IAAI,MAAM,SAAS,KAAK,IAAI;YACpC;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,gBAAgB;YAC9B,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;YAC7B,MAAM;QACR,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,WAAW,OAAO;QACtB,IAAI;YACF,aAAa;YACb,MAAM,WAAW,MAAM,iHAAA,CAAA,UAAS,CAAC,QAAQ,CAAC;YAE1C,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,MAAM,EAAE,OAAO,QAAQ,EAAE,MAAM,QAAQ,EAAE,GAAG,SAAS,IAAI;gBAEzD,wBAAwB;gBACxB,qJAAA,CAAA,UAAO,CAAC,GAAG,CAAC,SAAS,UAAU;oBAC7B,SAAS;oBACT,QAAQ,oDAAyB;oBACjC,UAAU;gBACZ;gBAEA,SAAS;gBACT,QAAQ;gBAER,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;gBACd,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,MAAM,IAAI,MAAM,SAAS,KAAK,IAAI;YACpC;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,uBAAuB;YACrC,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;YAC7B,MAAM;QACR,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,SAAS;QACb,IAAI;YACF,uBAAuB;YACvB,MAAM,iHAAA,CAAA,UAAS,CAAC,MAAM;QACxB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC,SAAU;YACR,kDAAkD;YAClD,qJAAA,CAAA,UAAO,CAAC,MAAM,CAAC;YACf,SAAS;YACT,QAAQ;YACR,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;YACd,OAAO,IAAI,CAAC;QACd;IACF;IAEA,MAAM,gBAAgB,OAAO;QAC3B,IAAI;YACF,MAAM,WAAW,MAAM,iHAAA,CAAA,UAAS,CAAC,aAAa,CAAC;YAE/C,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,QAAQ,SAAS,IAAI,CAAC,IAAI;gBAC1B,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;YAChB,OAAO;gBACL,MAAM,IAAI,MAAM,SAAS,KAAK,IAAI;YACpC;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,yBAAyB;YACvC,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;YAC7B,MAAM;QACR;IACF;IAEA,MAAM,QAAyB;QAC7B;QACA;QACA;QACA,iBAAiB,CAAC,CAAC,QAAQ,CAAC,CAAC;QAC7B;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;AAEO,MAAM,UAAU;IACrB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;uCAEe", "debugId": null}}, {"offset": {"line": 625, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/trendz/frontend/src/contexts/LanguageContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';\nimport enTranslations from '../locales/en.json';\nimport arTranslations from '../locales/ar.json';\n\ntype Language = 'en' | 'ar';\n\ninterface LanguageContextType {\n  language: Language;\n  setLanguage: (lang: Language) => void;\n  t: (key: string) => string;\n  isRTL: boolean;\n}\n\nconst LanguageContext = createContext<LanguageContextType | undefined>(undefined);\n\nconst translations = {\n  en: enTranslations,\n  ar: arTranslations\n};\n\nexport function LanguageProvider({ children }: { children: ReactNode }) {\n  const [language, setLanguage] = useState<Language>('en');\n\n  useEffect(() => {\n    // Load language from localStorage\n    const savedLanguage = localStorage.getItem('language') as Language;\n    if (savedLanguage && (savedLanguage === 'en' || savedLanguage === 'ar')) {\n      setLanguage(savedLanguage);\n    } else {\n      // Detect browser language\n      const browserLang = navigator.language.toLowerCase();\n      if (browserLang.startsWith('ar')) {\n        setLanguage('ar');\n      }\n    }\n  }, []);\n\n  useEffect(() => {\n    // Save language to localStorage\n    localStorage.setItem('language', language);\n    \n    // Update document direction and language\n    document.documentElement.lang = language;\n    document.documentElement.dir = language === 'ar' ? 'rtl' : 'ltr';\n    \n    // Update body class for RTL styling\n    if (language === 'ar') {\n      document.body.classList.add('rtl');\n    } else {\n      document.body.classList.remove('rtl');\n    }\n  }, [language]);\n\n  const t = (key: string): string => {\n    const keys = key.split('.');\n    let value: any = translations[language];\n    \n    for (const k of keys) {\n      if (value && typeof value === 'object' && k in value) {\n        value = value[k];\n      } else {\n        // Fallback to English if key not found\n        value = translations.en;\n        for (const fallbackKey of keys) {\n          if (value && typeof value === 'object' && fallbackKey in value) {\n            value = value[fallbackKey];\n          } else {\n            return key; // Return key if not found in fallback\n          }\n        }\n        break;\n      }\n    }\n    \n    return typeof value === 'string' ? value : key;\n  };\n\n  const value = {\n    language,\n    setLanguage,\n    t,\n    isRTL: language === 'ar'\n  };\n\n  return (\n    <LanguageContext.Provider value={value}>\n      {children}\n    </LanguageContext.Provider>\n  );\n}\n\nexport function useLanguage() {\n  const context = useContext(LanguageContext);\n  if (context === undefined) {\n    throw new Error('useLanguage must be used within a LanguageProvider');\n  }\n  return context;\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAJA;;;;;AAeA,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAmC;AAEvE,MAAM,eAAe;IACnB,IAAI,4FAAA,CAAA,UAAc;IAClB,IAAI,4FAAA,CAAA,UAAc;AACpB;AAEO,SAAS,iBAAiB,EAAE,QAAQ,EAA2B;IACpE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY;IAEnD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,kCAAkC;QAClC,MAAM,gBAAgB,aAAa,OAAO,CAAC;QAC3C,IAAI,iBAAiB,CAAC,kBAAkB,QAAQ,kBAAkB,IAAI,GAAG;YACvE,YAAY;QACd,OAAO;YACL,0BAA0B;YAC1B,MAAM,cAAc,UAAU,QAAQ,CAAC,WAAW;YAClD,IAAI,YAAY,UAAU,CAAC,OAAO;gBAChC,YAAY;YACd;QACF;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,gCAAgC;QAChC,aAAa,OAAO,CAAC,YAAY;QAEjC,yCAAyC;QACzC,SAAS,eAAe,CAAC,IAAI,GAAG;QAChC,SAAS,eAAe,CAAC,GAAG,GAAG,aAAa,OAAO,QAAQ;QAE3D,oCAAoC;QACpC,IAAI,aAAa,MAAM;YACrB,SAAS,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;QAC9B,OAAO;YACL,SAAS,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;QACjC;IACF,GAAG;QAAC;KAAS;IAEb,MAAM,IAAI,CAAC;QACT,MAAM,OAAO,IAAI,KAAK,CAAC;QACvB,IAAI,QAAa,YAAY,CAAC,SAAS;QAEvC,KAAK,MAAM,KAAK,KAAM;YACpB,IAAI,SAAS,OAAO,UAAU,YAAY,KAAK,OAAO;gBACpD,QAAQ,KAAK,CAAC,EAAE;YAClB,OAAO;gBACL,uCAAuC;gBACvC,QAAQ,aAAa,EAAE;gBACvB,KAAK,MAAM,eAAe,KAAM;oBAC9B,IAAI,SAAS,OAAO,UAAU,YAAY,eAAe,OAAO;wBAC9D,QAAQ,KAAK,CAAC,YAAY;oBAC5B,OAAO;wBACL,OAAO,KAAK,sCAAsC;oBACpD;gBACF;gBACA;YACF;QACF;QAEA,OAAO,OAAO,UAAU,WAAW,QAAQ;IAC7C;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA,OAAO,aAAa;IACtB;IAEA,qBACE,8OAAC,gBAAgB,QAAQ;QAAC,OAAO;kBAC9B;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 720, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/trendz/frontend/src/contexts/ThemeContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';\n\ntype Theme = 'light' | 'dark' | 'system';\n\ninterface ThemeContextType {\n  theme: Theme;\n  setTheme: (theme: Theme) => void;\n  isDark: boolean;\n}\n\nconst ThemeContext = createContext<ThemeContextType | undefined>(undefined);\n\nexport function ThemeProvider({ children }: { children: ReactNode }) {\n  const [theme, setTheme] = useState<Theme>('system');\n  const [isDark, setIsDark] = useState(false);\n\n  useEffect(() => {\n    // Load theme from localStorage\n    const savedTheme = localStorage.getItem('theme') as Theme;\n    if (savedTheme && ['light', 'dark', 'system'].includes(savedTheme)) {\n      setTheme(savedTheme);\n    }\n  }, []);\n\n  useEffect(() => {\n    const updateTheme = () => {\n      let shouldBeDark = false;\n\n      if (theme === 'dark') {\n        shouldBeDark = true;\n      } else if (theme === 'light') {\n        shouldBeDark = false;\n      } else {\n        // System theme\n        shouldBeDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n      }\n\n      setIsDark(shouldBeDark);\n      \n      // Update document class\n      if (shouldBeDark) {\n        document.documentElement.classList.add('dark');\n      } else {\n        document.documentElement.classList.remove('dark');\n      }\n    };\n\n    updateTheme();\n\n    // Save theme to localStorage\n    localStorage.setItem('theme', theme);\n\n    // Listen for system theme changes\n    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n    const handleChange = () => {\n      if (theme === 'system') {\n        updateTheme();\n      }\n    };\n\n    mediaQuery.addEventListener('change', handleChange);\n    return () => mediaQuery.removeEventListener('change', handleChange);\n  }, [theme]);\n\n  const value = {\n    theme,\n    setTheme,\n    isDark\n  };\n\n  return (\n    <ThemeContext.Provider value={value}>\n      {children}\n    </ThemeContext.Provider>\n  );\n}\n\nexport function useTheme() {\n  const context = useContext(ThemeContext);\n  if (context === undefined) {\n    throw new Error('useTheme must be used within a ThemeProvider');\n  }\n  return context;\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAYA,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAgC;AAE1D,SAAS,cAAc,EAAE,QAAQ,EAA2B;IACjE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS;IAC1C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,+BAA+B;QAC/B,MAAM,aAAa,aAAa,OAAO,CAAC;QACxC,IAAI,cAAc;YAAC;YAAS;YAAQ;SAAS,CAAC,QAAQ,CAAC,aAAa;YAClE,SAAS;QACX;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,cAAc;YAClB,IAAI,eAAe;YAEnB,IAAI,UAAU,QAAQ;gBACpB,eAAe;YACjB,OAAO,IAAI,UAAU,SAAS;gBAC5B,eAAe;YACjB,OAAO;gBACL,eAAe;gBACf,eAAe,OAAO,UAAU,CAAC,gCAAgC,OAAO;YAC1E;YAEA,UAAU;YAEV,wBAAwB;YACxB,IAAI,cAAc;gBAChB,SAAS,eAAe,CAAC,SAAS,CAAC,GAAG,CAAC;YACzC,OAAO;gBACL,SAAS,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC;YAC5C;QACF;QAEA;QAEA,6BAA6B;QAC7B,aAAa,OAAO,CAAC,SAAS;QAE9B,kCAAkC;QAClC,MAAM,aAAa,OAAO,UAAU,CAAC;QACrC,MAAM,eAAe;YACnB,IAAI,UAAU,UAAU;gBACtB;YACF;QACF;QAEA,WAAW,gBAAgB,CAAC,UAAU;QACtC,OAAO,IAAM,WAAW,mBAAmB,CAAC,UAAU;IACxD,GAAG;QAAC;KAAM;IAEV,MAAM,QAAQ;QACZ;QACA;QACA;IACF;IAEA,qBACE,8OAAC,aAAa,QAAQ;QAAC,OAAO;kBAC3B;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}]}