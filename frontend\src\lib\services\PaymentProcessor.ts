export interface Payment {
  id: string;
  userId: string;
  type: 'deposit' | 'order' | 'refund';
  method: 'PayPal' | 'Vodafone Cash' | 'InstaPay' | 'Bank Transfer' | 'Balance';
  amount: number;
  fee: number;
  netAmount: number;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
  currency: string;
  transactionId?: string;
  orderId?: string;
  gateway: string;
  gatewayResponse?: any;
  createdAt: Date;
  updatedAt: Date;
  completedAt?: Date;
  notes?: string;
}

export interface UserBalance {
  userId: string;
  balance: number;
  totalDeposited: number;
  totalSpent: number;
  currency: string;
  lastUpdated: Date;
}

export interface PaymentMethod {
  id: string;
  name: string;
  type: 'gateway' | 'manual';
  enabled: boolean;
  minAmount: number;
  maxAmount: number;
  fee: number; // percentage
  fixedFee: number;
  processingTime: string;
  instructions?: string;
  config: any;
}

export class PaymentProcessor {
  private static instance: PaymentProcessor;
  private payments: Map<string, Payment> = new Map();
  private userBalances: Map<string, UserBalance> = new Map();
  private paymentMethods: Map<string, PaymentMethod> = new Map();

  private constructor() {
    this.initializePaymentMethods();
    this.initializeMockData();
  }

  public static getInstance(): PaymentProcessor {
    if (!PaymentProcessor.instance) {
      PaymentProcessor.instance = new PaymentProcessor();
    }
    return PaymentProcessor.instance;
  }

  private initializePaymentMethods() {
    const methods: PaymentMethod[] = [
      {
        id: 'paypal',
        name: 'PayPal',
        type: 'gateway',
        enabled: true,
        minAmount: 5.00,
        maxAmount: 1000.00,
        fee: 0.035, // 3.5%
        fixedFee: 0.30,
        processingTime: 'Instant',
        config: {
          clientId: 'your-paypal-client-id',
          clientSecret: 'your-paypal-client-secret',
          environment: 'sandbox' // or 'production'
        }
      },
      {
        id: 'vodafone_cash',
        name: 'Vodafone Cash',
        type: 'manual',
        enabled: true,
        minAmount: 10.00,
        maxAmount: 5000.00,
        fee: 0.00,
        fixedFee: 0.00,
        processingTime: '1-2 hours',
        instructions: 'Send money to: 0**********\nThen upload receipt',
        config: {
          phoneNumber: '+20**********'
        }
      },
      {
        id: 'instapay',
        name: 'InstaPay',
        type: 'manual',
        enabled: true,
        minAmount: 5.00,
        maxAmount: 2000.00,
        fee: 0.00,
        fixedFee: 0.00,
        processingTime: '30 minutes',
        instructions: 'Send to InstaPay: your-instapay-account',
        config: {
          account: 'your-instapay-account'
        }
      },
      {
        id: 'bank_transfer',
        name: 'Bank Transfer',
        type: 'manual',
        enabled: true,
        minAmount: 20.00,
        maxAmount: 10000.00,
        fee: 0.00,
        fixedFee: 0.00,
        processingTime: '2-24 hours',
        instructions: 'Bank: National Bank of Egypt\nAccount: **********\nIBAN: **************************',
        config: {
          bankName: 'National Bank of Egypt',
          accountNumber: '**********',
          iban: '**************************'
        }
      }
    ];

    methods.forEach(method => {
      this.paymentMethods.set(method.id, method);
    });
  }

  private initializeMockData() {
    // Initialize some mock user balances
    const mockBalances: UserBalance[] = [
      {
        userId: 'user1',
        balance: 125.50,
        totalDeposited: 200.00,
        totalSpent: 74.50,
        currency: 'USD',
        lastUpdated: new Date()
      },
      {
        userId: 'user2',
        balance: 89.30,
        totalDeposited: 150.00,
        totalSpent: 60.70,
        currency: 'USD',
        lastUpdated: new Date()
      }
    ];

    mockBalances.forEach(balance => {
      this.userBalances.set(balance.userId, balance);
    });
  }

  public async createDeposit(
    userId: string,
    amount: number,
    methodId: string,
    currency: string = 'USD'
  ): Promise<Payment> {
    const method = this.paymentMethods.get(methodId);
    if (!method || !method.enabled) {
      throw new Error('Payment method not available');
    }

    if (amount < method.minAmount || amount > method.maxAmount) {
      throw new Error(`Amount must be between $${method.minAmount} and $${method.maxAmount}`);
    }

    const fee = this.calculateFee(amount, method);
    const netAmount = amount - fee;
    const paymentId = this.generatePaymentId();

    const payment: Payment = {
      id: paymentId,
      userId,
      type: 'deposit',
      method: method.name as any,
      amount,
      fee,
      netAmount,
      status: method.type === 'gateway' ? 'processing' : 'pending',
      currency,
      gateway: method.name,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    this.payments.set(paymentId, payment);

    // Process payment based on method type
    if (method.type === 'gateway') {
      await this.processGatewayPayment(payment, method);
    } else {
      await this.processManualPayment(payment, method);
    }

    return payment;
  }

  private calculateFee(amount: number, method: PaymentMethod): number {
    const percentageFee = amount * method.fee;
    return percentageFee + method.fixedFee;
  }

  private async processGatewayPayment(payment: Payment, method: PaymentMethod) {
    try {
      // Simulate gateway processing
      console.log(`Processing ${method.name} payment ${payment.id}`);
      
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Mock gateway response
      const success = Math.random() > 0.1; // 90% success rate
      
      if (success) {
        payment.status = 'completed';
        payment.transactionId = `${method.id}_${Date.now()}`;
        payment.completedAt = new Date();
        
        // Add to user balance
        await this.addToBalance(payment.userId, payment.netAmount);
        
        console.log(`Payment ${payment.id} completed successfully`);
      } else {
        payment.status = 'failed';
        payment.notes = 'Gateway processing failed';
        console.log(`Payment ${payment.id} failed`);
      }
      
      payment.updatedAt = new Date();
      this.payments.set(payment.id, payment);
      
    } catch (error) {
      payment.status = 'failed';
      payment.notes = `Error: ${error}`;
      payment.updatedAt = new Date();
      this.payments.set(payment.id, payment);
    }
  }

  private async processManualPayment(payment: Payment, method: PaymentMethod) {
    // Manual payments require admin approval
    console.log(`Manual payment ${payment.id} created, waiting for admin approval`);
    
    // In a real application, this would send notifications to admins
    // For demo purposes, we'll auto-approve after a delay
    setTimeout(async () => {
      const currentPayment = this.payments.get(payment.id);
      if (currentPayment && currentPayment.status === 'pending') {
        await this.approveManualPayment(payment.id);
      }
    }, 30000); // Auto-approve after 30 seconds for demo
  }

  public async approveManualPayment(paymentId: string): Promise<boolean> {
    const payment = this.payments.get(paymentId);
    if (!payment || payment.status !== 'pending') {
      return false;
    }

    payment.status = 'completed';
    payment.completedAt = new Date();
    payment.updatedAt = new Date();
    payment.transactionId = `manual_${Date.now()}`;

    // Add to user balance
    await this.addToBalance(payment.userId, payment.netAmount);

    this.payments.set(paymentId, payment);
    console.log(`Manual payment ${paymentId} approved`);
    
    return true;
  }

  public async rejectManualPayment(paymentId: string, reason: string): Promise<boolean> {
    const payment = this.payments.get(paymentId);
    if (!payment || payment.status !== 'pending') {
      return false;
    }

    payment.status = 'failed';
    payment.notes = `Rejected: ${reason}`;
    payment.updatedAt = new Date();

    this.payments.set(paymentId, payment);
    console.log(`Manual payment ${paymentId} rejected: ${reason}`);
    
    return true;
  }

  public async processOrderPayment(
    userId: string,
    orderId: string,
    amount: number,
    currency: string = 'USD'
  ): Promise<Payment> {
    const userBalance = this.getUserBalance(userId);
    
    if (userBalance.balance < amount) {
      throw new Error('Insufficient balance');
    }

    const paymentId = this.generatePaymentId();
    const payment: Payment = {
      id: paymentId,
      userId,
      type: 'order',
      method: 'Balance',
      amount: -amount, // Negative for deduction
      fee: 0,
      netAmount: -amount,
      status: 'completed',
      currency,
      orderId,
      gateway: 'Internal',
      createdAt: new Date(),
      updatedAt: new Date(),
      completedAt: new Date()
    };

    // Deduct from user balance
    await this.deductFromBalance(userId, amount);

    this.payments.set(paymentId, payment);
    console.log(`Order payment ${paymentId} processed for order ${orderId}`);
    
    return payment;
  }

  public async processRefund(
    userId: string,
    orderId: string,
    amount: number,
    reason: string,
    currency: string = 'USD'
  ): Promise<Payment> {
    const paymentId = this.generatePaymentId();
    const payment: Payment = {
      id: paymentId,
      userId,
      type: 'refund',
      method: 'Balance',
      amount,
      fee: 0,
      netAmount: amount,
      status: 'completed',
      currency,
      orderId,
      gateway: 'Internal',
      notes: reason,
      createdAt: new Date(),
      updatedAt: new Date(),
      completedAt: new Date()
    };

    // Add to user balance
    await this.addToBalance(userId, amount);

    this.payments.set(paymentId, payment);
    console.log(`Refund ${paymentId} processed for order ${orderId}: ${reason}`);
    
    return payment;
  }

  private async addToBalance(userId: string, amount: number) {
    let balance = this.userBalances.get(userId);
    
    if (!balance) {
      balance = {
        userId,
        balance: 0,
        totalDeposited: 0,
        totalSpent: 0,
        currency: 'USD',
        lastUpdated: new Date()
      };
    }

    balance.balance += amount;
    if (amount > 0) {
      balance.totalDeposited += amount;
    }
    balance.lastUpdated = new Date();

    this.userBalances.set(userId, balance);
  }

  private async deductFromBalance(userId: string, amount: number) {
    const balance = this.userBalances.get(userId);
    
    if (!balance || balance.balance < amount) {
      throw new Error('Insufficient balance');
    }

    balance.balance -= amount;
    balance.totalSpent += amount;
    balance.lastUpdated = new Date();

    this.userBalances.set(userId, balance);
  }

  public getUserBalance(userId: string): UserBalance {
    let balance = this.userBalances.get(userId);
    
    if (!balance) {
      balance = {
        userId,
        balance: 0,
        totalDeposited: 0,
        totalSpent: 0,
        currency: 'USD',
        lastUpdated: new Date()
      };
      this.userBalances.set(userId, balance);
    }

    return balance;
  }

  public getPayment(paymentId: string): Payment | undefined {
    return this.payments.get(paymentId);
  }

  public getUserPayments(userId: string): Payment[] {
    return Array.from(this.payments.values())
      .filter(payment => payment.userId === userId)
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
  }

  public getAllPayments(): Payment[] {
    return Array.from(this.payments.values())
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
  }

  public getPaymentMethods(): PaymentMethod[] {
    return Array.from(this.paymentMethods.values())
      .filter(method => method.enabled);
  }

  public getPaymentMethod(methodId: string): PaymentMethod | undefined {
    return this.paymentMethods.get(methodId);
  }

  private generatePaymentId(): string {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substr(2, 5);
    return `PAY${timestamp}${random}`.toUpperCase();
  }

  public getPaymentStats(): {
    totalRevenue: number;
    totalRefunds: number;
    pendingAmount: number;
    totalTransactions: number;
  } {
    const payments = Array.from(this.payments.values());

    const totalRevenue = payments
      .filter(p => p.type === 'deposit' && p.status === 'completed')
      .reduce((sum, p) => sum + p.netAmount, 0);

    const totalRefunds = payments
      .filter(p => p.type === 'refund' && p.status === 'completed')
      .reduce((sum, p) => sum + p.amount, 0);

    const pendingAmount = payments
      .filter(p => p.status === 'pending')
      .reduce((sum, p) => sum + Math.abs(p.amount), 0);

    return {
      totalRevenue,
      totalRefunds,
      pendingAmount,
      totalTransactions: payments.length
    };
  }
}
}
