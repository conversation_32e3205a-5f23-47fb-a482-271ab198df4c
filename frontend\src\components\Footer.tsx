'use client';

import Link from 'next/link';
import { useLanguage } from '../contexts/LanguageContext';

export default function Footer() {
  const { t, isRTL } = useLanguage();

  return (
    <footer className={`bg-gray-900 text-white py-12 ${isRTL ? 'rtl' : 'ltr'}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div>
            <div className="flex items-center space-x-2 rtl:space-x-reverse mb-4">
              <div className="w-8 h-8 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">T</span>
              </div>
              <span className="text-xl font-bold">Trendz</span>
            </div>
            <p className="text-gray-400">{t('footer.description')}</p>
          </div>
          
          <div>
            <h3 className="text-lg font-semibold mb-4">{t('navigation.services')}</h3>
            <ul className="space-y-2 text-gray-400">
              <li><Link href="/services" className="hover:text-white">{t('navigation.services')}</Link></li>
              <li><Link href="/order" className="hover:text-white">{t('orders.title')}</Link></li>
              <li><Link href="/orders" className="hover:text-white">{t('orders.title')}</Link></li>
            </ul>
          </div>
          
          <div>
            <h3 className="text-lg font-semibold mb-4">{t('navigation.support')}</h3>
            <ul className="space-y-2 text-gray-400">
              <li><Link href="/support" className="hover:text-white">{t('navigation.support')}</Link></li>
              <li><Link href="/faq" className="hover:text-white">{t('navigation.faq')}</Link></li>
              <li><Link href="/terms" className="hover:text-white">{t('footer.termsOfService')}</Link></li>
              <li><Link href="/privacy" className="hover:text-white">{t('footer.privacyPolicy')}</Link></li>
            </ul>
          </div>
          
          <div>
            <h3 className="text-lg font-semibold mb-4">{t('navigation.profile')}</h3>
            <ul className="space-y-2 text-gray-400">
              <li><Link href="/auth/login" className="hover:text-white">{t('navigation.login')}</Link></li>
              <li><Link href="/auth/register" className="hover:text-white">{t('navigation.register')}</Link></li>
              <li><Link href="/dashboard" className="hover:text-white">{t('navigation.dashboard')}</Link></li>
              <li><Link href="/profile" className="hover:text-white">{t('navigation.profile')}</Link></li>
            </ul>
          </div>
        </div>
        
        <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
          <p>&copy; 2025 Trendz SMM Panel. {t('footer.allRightsReserved')}.</p>
        </div>
      </div>
    </footer>
  );
}
