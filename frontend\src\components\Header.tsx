'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useLanguage } from '../contexts/LanguageContext';
import { useTheme } from '../contexts/ThemeContext';

interface HeaderProps {
  currentPage?: string;
  isAdmin?: boolean;
  user?: {
    name: string;
    balance?: number;
  } | null;
}

export default function Header({ currentPage, isAdmin = false, user = null }: HeaderProps) {
  const { language, setLanguage, t, isRTL } = useLanguage();
  const { theme, setTheme, isDark } = useTheme();
  const [showUserMenu, setShowUserMenu] = useState(false);
  const [showLanguageMenu, setShowLanguageMenu] = useState(false);
  const [showThemeMenu, setShowThemeMenu] = useState(false);

  const navigation = isAdmin ? [
    { name: t('admin.title'), href: '/admin', current: currentPage === 'admin' },
    { name: t('admin.userManagement'), href: '/admin/users', current: currentPage === 'users' },
    { name: t('admin.orderManagement'), href: '/admin/orders', current: currentPage === 'orders' },
    { name: t('admin.serviceManagement'), href: '/admin/services', current: currentPage === 'services' },
    { name: t('admin.paymentManagement'), href: '/admin/payments', current: currentPage === 'payments' },
    { name: t('admin.settings'), href: '/admin/settings', current: currentPage === 'settings' }
  ] : [
    { name: t('navigation.services'), href: '/services', current: currentPage === 'services' },
    { name: t('navigation.faq'), href: '/faq', current: currentPage === 'faq' },
    { name: t('navigation.support'), href: '/support', current: currentPage === 'support' },
    { name: t('navigation.dashboard'), href: '/dashboard', current: currentPage === 'dashboard' }
  ];

  const userNavigation = user ? [
    { name: t('navigation.dashboard'), href: '/dashboard' },
    { name: t('navigation.orders'), href: '/orders' },
    { name: t('navigation.payments'), href: '/payments' },
    { name: t('navigation.profile'), href: '/profile' },
    { name: t('navigation.logout'), href: '/auth/logout' }
  ] : [
    { name: t('navigation.login'), href: '/auth/login' },
    { name: t('navigation.register'), href: '/auth/register' }
  ];

  return (
    <div className={`bg-white dark:bg-gray-900 shadow ${isRTL ? 'rtl' : 'ltr'}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center py-6">
          {/* Logo */}
          <div className="flex items-center">
            <Link href="/" className="flex items-center space-x-2 rtl:space-x-reverse">
              <div className="w-8 h-8 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">T</span>
              </div>
              <span className="text-xl font-bold text-gray-900 dark:text-white">
                Trendz {isAdmin && t('navigation.admin')}
              </span>
            </Link>
          </div>
          
          {/* Navigation */}
          <nav className="hidden md:flex space-x-8 rtl:space-x-reverse">
            {navigation.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className={`${
                  item.current
                    ? 'text-purple-600 font-medium'
                    : 'text-gray-600 dark:text-gray-300 hover:text-purple-600 dark:hover:text-purple-400'
                } transition-colors`}
              >
                {item.name}
              </Link>
            ))}
          </nav>

          {/* Right side controls */}
          <div className="flex items-center space-x-4 rtl:space-x-reverse">
            {/* Theme Toggle */}
            <div className="relative">
              <button
                onClick={() => setShowThemeMenu(!showThemeMenu)}
                className="p-2 text-gray-600 dark:text-gray-300 hover:text-purple-600 dark:hover:text-purple-400 transition-colors"
                title={t('theme.light')}
              >
                {isDark ? (
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
                  </svg>
                ) : (
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                  </svg>
                )}
              </button>
              
              {showThemeMenu && (
                <div className={`absolute ${isRTL ? 'left-0' : 'right-0'} mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg py-1 z-50`}>
                  <button
                    onClick={() => { setTheme('light'); setShowThemeMenu(false); }}
                    className={`block w-full text-left px-4 py-2 text-sm ${theme === 'light' ? 'bg-purple-50 dark:bg-purple-900 text-purple-600 dark:text-purple-400' : 'text-gray-700 dark:text-gray-300'} hover:bg-gray-100 dark:hover:bg-gray-700`}
                  >
                    <div className="flex items-center">
                      <svg className="w-4 h-4 mr-2 rtl:ml-2 rtl:mr-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                      </svg>
                      {t('theme.light')}
                    </div>
                  </button>
                  <button
                    onClick={() => { setTheme('dark'); setShowThemeMenu(false); }}
                    className={`block w-full text-left px-4 py-2 text-sm ${theme === 'dark' ? 'bg-purple-50 dark:bg-purple-900 text-purple-600 dark:text-purple-400' : 'text-gray-700 dark:text-gray-300'} hover:bg-gray-100 dark:hover:bg-gray-700`}
                  >
                    <div className="flex items-center">
                      <svg className="w-4 h-4 mr-2 rtl:ml-2 rtl:mr-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
                      </svg>
                      {t('theme.dark')}
                    </div>
                  </button>
                  <button
                    onClick={() => { setTheme('system'); setShowThemeMenu(false); }}
                    className={`block w-full text-left px-4 py-2 text-sm ${theme === 'system' ? 'bg-purple-50 dark:bg-purple-900 text-purple-600 dark:text-purple-400' : 'text-gray-700 dark:text-gray-300'} hover:bg-gray-100 dark:hover:bg-gray-700`}
                  >
                    <div className="flex items-center">
                      <svg className="w-4 h-4 mr-2 rtl:ml-2 rtl:mr-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                      </svg>
                      {t('theme.system')}
                    </div>
                  </button>
                </div>
              )}
            </div>

            {/* Language Toggle */}
            <div className="relative">
              <button
                onClick={() => setShowLanguageMenu(!showLanguageMenu)}
                className="flex items-center space-x-1 rtl:space-x-reverse text-gray-600 dark:text-gray-300 hover:text-purple-600 dark:hover:text-purple-400 transition-colors"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129" />
                </svg>
                <span className="text-sm font-medium">
                  {language === 'ar' ? 'ع' : 'EN'}
                </span>
              </button>
              
              {showLanguageMenu && (
                <div className={`absolute ${isRTL ? 'left-0' : 'right-0'} mt-2 w-32 bg-white dark:bg-gray-800 rounded-md shadow-lg py-1 z-50`}>
                  <button
                    onClick={() => { setLanguage('en'); setShowLanguageMenu(false); }}
                    className={`block w-full text-left px-4 py-2 text-sm ${language === 'en' ? 'bg-purple-50 dark:bg-purple-900 text-purple-600 dark:text-purple-400' : 'text-gray-700 dark:text-gray-300'} hover:bg-gray-100 dark:hover:bg-gray-700`}
                  >
                    {t('language.english')}
                  </button>
                  <button
                    onClick={() => { setLanguage('ar'); setShowLanguageMenu(false); }}
                    className={`block w-full text-left px-4 py-2 text-sm ${language === 'ar' ? 'bg-purple-50 dark:bg-purple-900 text-purple-600 dark:text-purple-400' : 'text-gray-700 dark:text-gray-300'} hover:bg-gray-100 dark:hover:bg-gray-700`}
                  >
                    {t('language.arabic')}
                  </button>
                </div>
              )}
            </div>

            {/* User Menu */}
            {user ? (
              <div className="relative flex items-center">
                {user.balance !== undefined && (
                  <div className="bg-gray-100 dark:bg-gray-700 px-3 py-1 rounded-full mr-2 rtl:ml-2 rtl:mr-0">
                    <span className="text-sm font-medium text-gray-900 dark:text-white">
                      ${user.balance.toFixed(2)}
                    </span>
                  </div>
                )}
                <button
                  onClick={() => setShowUserMenu(!showUserMenu)}
                  className="w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center"
                >
                  <span className="text-white text-sm font-medium">
                    {user.name.charAt(0).toUpperCase()}
                  </span>
                </button>

                {showUserMenu && (
                  <div className={`absolute ${isRTL ? 'left-0' : 'right-0'} mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg py-1 z-50`}>
                    {userNavigation.map((item) => (
                      <Link
                        key={item.name}
                        href={item.href}
                        className="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                        onClick={() => setShowUserMenu(false)}
                      >
                        {item.name}
                      </Link>
                    ))}
                  </div>
                )}
              </div>
            ) : (
              <div className="flex items-center space-x-4 rtl:space-x-reverse">
                <Link
                  href="/auth/login"
                  className="text-gray-600 dark:text-gray-300 hover:text-purple-600 dark:hover:text-purple-400"
                >
                  {t('navigation.login')}
                </Link>
                <Link
                  href="/auth/register"
                  className="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 transition-colors"
                >
                  {t('navigation.register')}
                </Link>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
