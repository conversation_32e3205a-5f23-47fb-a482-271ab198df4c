{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/trendz/frontend/src/app/services/page.tsx"], "sourcesContent": ["import Link from 'next/link';\n\nexport default function Services() {\n  const platforms = [\n    { name: 'Facebook', icon: '', color: 'bg-blue-500', services: ['Likes', 'Followers', 'Comments', 'Shares'] },\n    { name: 'Instagram', icon: '', color: 'bg-pink-500', services: ['Followers', 'Likes', 'Comments', 'Story Views'] },\n    { name: 'TikTok', icon: '', color: 'bg-black', services: ['Views', 'Likes', 'Followers', 'Comments'] },\n    { name: 'YouTube', icon: '', color: 'bg-red-500', services: ['Views', 'Subscribers', 'Likes', 'Comments'] },\n    { name: 'Twitter', icon: '', color: 'bg-blue-400', services: ['Followers', 'Likes', 'Retweets', 'Comments'] },\n    { name: 'Snapchat', icon: '', color: 'bg-yellow-400', services: ['Views', 'Followers'] }\n  ];\n\n  const featuredServices = [\n    {\n      platform: 'Instagram',\n      service: 'Instagram Followers',\n      price: '.50',\n      per: '1000',\n      quality: 'High Quality',\n      speed: 'Fast',\n      icon: ''\n    },\n    {\n      platform: 'TikTok',\n      service: 'TikTok Views',\n      price: '.20',\n      per: '1000',\n      quality: 'Premium',\n      speed: 'Instant',\n      icon: ''\n    },\n    {\n      platform: 'YouTube',\n      service: 'YouTube Views',\n      price: '.00',\n      per: '1000',\n      quality: 'High Retention',\n      speed: 'Fast',\n      icon: ''\n    },\n    {\n      platform: 'Facebook',\n      service: 'Facebook Likes',\n      price: '.80',\n      per: '1000',\n      quality: 'Real Users',\n      speed: 'Medium',\n      icon: ''\n    }\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <div className=\"bg-gradient-to-r from-purple-600 to-blue-600 text-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16\">\n          <div className=\"text-center\">\n            <h1 className=\"text-4xl font-bold mb-4\">Our Services</h1>\n            <p className=\"text-xl text-purple-100 max-w-2xl mx-auto\">\n              Choose from our wide range of social media marketing services to boost your online presence\n            </p>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        {/* Platform Categories */}\n        <div className=\"mb-12\">\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-8 text-center\">Choose Your Platform</h2>\n          <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6\">\n            {platforms.map((platform, index) => (\n              <div key={index} className=\"bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow cursor-pointer group\">\n                <div className={`w-16 h-16 ${platform.color} rounded-lg flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform`}>\n                  <span className=\"text-3xl\">{platform.icon}</span>\n                </div>\n                <h3 className=\"font-semibold text-center text-gray-900 mb-2\">{platform.name}</h3>\n                <div className=\"text-xs text-gray-600 text-center\">\n                  {platform.services.map((service, idx) => (\n                    <span key={idx} className=\"inline-block bg-gray-100 rounded-full px-2 py-1 mr-1 mb-1\">\n                      {service}\n                    </span>\n                  ))}\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Featured Services */}\n        <div className=\"mb-12\">\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-8 text-center\">Featured Services</h2>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n            {featuredServices.map((service, index) => (\n              <div key={index} className=\"bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6\">\n                <div className=\"flex items-center mb-4\">\n                  <span className=\"text-2xl mr-3\">{service.icon}</span>\n                  <div>\n                    <h3 className=\"font-semibold text-gray-900\">{service.service}</h3>\n                    <p className=\"text-sm text-gray-600\">{service.platform}</p>\n                  </div>\n                </div>\n                \n                <div className=\"mb-4\">\n                  <div className=\"flex justify-between items-center mb-2\">\n                    <span className=\"text-2xl font-bold text-purple-600\">{service.price}</span>\n                    <span className=\"text-sm text-gray-600\">per {service.per}</span>\n                  </div>\n                  \n                  <div className=\"flex justify-between text-sm mb-2\">\n                    <span className=\"text-gray-600\">Quality:</span>\n                    <span className=\"font-medium text-green-600\">{service.quality}</span>\n                  </div>\n                  \n                  <div className=\"flex justify-between text-sm mb-4\">\n                    <span className=\"text-gray-600\">Speed:</span>\n                    <span className=\"font-medium text-blue-600\">{service.speed}</span>\n                  </div>\n                </div>\n                \n                <Link href={`/services/${index + 1}`} className=\"block w-full bg-purple-600 text-white py-2 px-4 rounded-md hover:bg-purple-700 transition-colors font-medium text-center\">\n                  Order Now\n                </Link>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Why Choose Us */}\n        <div className=\"bg-white rounded-lg shadow-md p-8\">\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-6 text-center\">Why Choose Our Services?</h2>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            <div className=\"text-center\">\n              <div className=\"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4\">\n                <svg className=\"w-6 h-6 text-green-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                </svg>\n              </div>\n              <h3 className=\"font-semibold text-gray-900 mb-2\">High Quality</h3>\n              <p className=\"text-gray-600\">All our services provide real, high-quality engagement from active users.</p>\n            </div>\n            \n            <div className=\"text-center\">\n              <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4\">\n                <svg className=\"w-6 h-6 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n                </svg>\n              </div>\n              <h3 className=\"font-semibold text-gray-900 mb-2\">Fast Delivery</h3>\n              <p className=\"text-gray-600\">Most orders start within minutes and complete within 24-48 hours.</p>\n            </div>\n            \n            <div className=\"text-center\">\n              <div className=\"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4\">\n                <svg className=\"w-6 h-6 text-purple-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 2.25a9.75 9.75 0 109.75 9.75A9.75 9.75 0 0012 2.25z\" />\n                </svg>\n              </div>\n              <h3 className=\"font-semibold text-gray-900 mb-2\">24/7 Support</h3>\n              <p className=\"text-gray-600\">Our customer support team is available round the clock to help you.</p>\n            </div>\n          </div>\n        </div>\n\n        {/* CTA Section */}\n        <div className=\"mt-12 text-center\">\n          <div className=\"bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg p-8 text-white\">\n            <h2 className=\"text-2xl font-bold mb-4\">Ready to Get Started?</h2>\n            <p className=\"text-purple-100 mb-6\">Join thousands of satisfied customers and boost your social media presence today!</p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <a href=\"/auth/register\" className=\"bg-white text-purple-600 px-6 py-3 rounded-md font-semibold hover:bg-gray-100 transition-colors\">\n                Create Free Account\n              </a>\n              <a href=\"/auth/login\" className=\"border-2 border-white text-white px-6 py-3 rounded-md font-semibold hover:bg-white hover:text-purple-600 transition-colors\">\n                Sign In\n              </a>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS;IACtB,MAAM,YAAY;QAChB;YAAE,MAAM;YAAY,MAAM;YAAI,OAAO;YAAe,UAAU;gBAAC;gBAAS;gBAAa;gBAAY;aAAS;QAAC;QAC3G;YAAE,MAAM;YAAa,MAAM;YAAI,OAAO;YAAe,UAAU;gBAAC;gBAAa;gBAAS;gBAAY;aAAc;QAAC;QACjH;YAAE,MAAM;YAAU,MAAM;YAAI,OAAO;YAAY,UAAU;gBAAC;gBAAS;gBAAS;gBAAa;aAAW;QAAC;QACrG;YAAE,MAAM;YAAW,MAAM;YAAI,OAAO;YAAc,UAAU;gBAAC;gBAAS;gBAAe;gBAAS;aAAW;QAAC;QAC1G;YAAE,MAAM;YAAW,MAAM;YAAI,OAAO;YAAe,UAAU;gBAAC;gBAAa;gBAAS;gBAAY;aAAW;QAAC;QAC5G;YAAE,MAAM;YAAY,MAAM;YAAI,OAAO;YAAiB,UAAU;gBAAC;gBAAS;aAAY;QAAC;KACxF;IAED,MAAM,mBAAmB;QACvB;YACE,UAAU;YACV,SAAS;YACT,OAAO;YACP,KAAK;YACL,SAAS;YACT,OAAO;YACP,MAAM;QACR;QACA;YACE,UAAU;YACV,SAAS;YACT,OAAO;YACP,KAAK;YACL,SAAS;YACT,OAAO;YACP,MAAM;QACR;QACA;YACE,UAAU;YACV,SAAS;YACT,OAAO;YACP,KAAK;YACL,SAAS;YACT,OAAO;YACP,MAAM;QACR;QACA;YACE,UAAU;YACV,SAAS;YACT,OAAO;YACP,KAAK;YACL,SAAS;YACT,OAAO;YACP,MAAM;QACR;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA0B;;;;;;0CACxC,8OAAC;gCAAE,WAAU;0CAA4C;;;;;;;;;;;;;;;;;;;;;;0BAO/D,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAClE,8OAAC;gCAAI,WAAU;0CACZ,UAAU,GAAG,CAAC,CAAC,UAAU,sBACxB,8OAAC;wCAAgB,WAAU;;0DACzB,8OAAC;gDAAI,WAAW,CAAC,UAAU,EAAE,SAAS,KAAK,CAAC,oGAAoG,CAAC;0DAC/I,cAAA,8OAAC;oDAAK,WAAU;8DAAY,SAAS,IAAI;;;;;;;;;;;0DAE3C,8OAAC;gDAAG,WAAU;0DAAgD,SAAS,IAAI;;;;;;0DAC3E,8OAAC;gDAAI,WAAU;0DACZ,SAAS,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,oBAC/B,8OAAC;wDAAe,WAAU;kEACvB;uDADQ;;;;;;;;;;;uCAPP;;;;;;;;;;;;;;;;kCAkBhB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAClE,8OAAC;gCAAI,WAAU;0CACZ,iBAAiB,GAAG,CAAC,CAAC,SAAS,sBAC9B,8OAAC;wCAAgB,WAAU;;0DACzB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAiB,QAAQ,IAAI;;;;;;kEAC7C,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAA+B,QAAQ,OAAO;;;;;;0EAC5D,8OAAC;gEAAE,WAAU;0EAAyB,QAAQ,QAAQ;;;;;;;;;;;;;;;;;;0DAI1D,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAsC,QAAQ,KAAK;;;;;;0EACnE,8OAAC;gEAAK,WAAU;;oEAAwB;oEAAK,QAAQ,GAAG;;;;;;;;;;;;;kEAG1D,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,8OAAC;gEAAK,WAAU;0EAA8B,QAAQ,OAAO;;;;;;;;;;;;kEAG/D,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,8OAAC;gEAAK,WAAU;0EAA6B,QAAQ,KAAK;;;;;;;;;;;;;;;;;;0DAI9D,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAM,CAAC,UAAU,EAAE,QAAQ,GAAG;gDAAE,WAAU;0DAA2H;;;;;;;uCA1BnK;;;;;;;;;;;;;;;;kCAmChB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAClE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;oDAAyB,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DAChF,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;0DAGzE,8OAAC;gDAAG,WAAU;0DAAmC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;kDAG/B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;oDAAwB,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DAC/E,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;0DAGzE,8OAAC;gDAAG,WAAU;0DAAmC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;kDAG/B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;oDAA0B,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACjF,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;0DAGzE,8OAAC;gDAAG,WAAU;0DAAmC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;;;;;;;;;;;;;kCAMnC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA0B;;;;;;8CACxC,8OAAC;oCAAE,WAAU;8CAAuB;;;;;;8CACpC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,MAAK;4CAAiB,WAAU;sDAAkG;;;;;;sDAGrI,8OAAC;4CAAE,MAAK;4CAAc,WAAU;sDAA6H;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS3K", "debugId": null}}]}