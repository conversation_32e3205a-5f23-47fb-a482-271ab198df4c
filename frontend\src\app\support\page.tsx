'use client';

import { useState } from 'react';
import Link from 'next/link';

export default function Support() {
  const [activeTab, setActiveTab] = useState('contact');
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    priority: 'medium',
    category: 'general',
    message: ''
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Support ticket submitted:', formData);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }));
  };

  const supportChannels = [
    {
      icon: '💬',
      title: 'Live Chat',
      description: 'Chat with our support team in real-time',
      availability: '24/7',
      responseTime: 'Instant',
      action: 'Start Chat'
    },
    {
      icon: '📧',
      title: 'Email Support',
      description: 'Send us an email and we\'ll get back to you',
      availability: '24/7',
      responseTime: '< 2 hours',
      action: 'Send Email'
    },
    {
      icon: '📱',
      title: 'WhatsApp',
      description: 'Contact us via WhatsApp for quick support',
      availability: '9 AM - 11 PM',
      responseTime: '< 30 minutes',
      action: 'Open WhatsApp'
    },
    {
      icon: '📞',
      title: 'Phone Support',
      description: 'Call us directly for urgent matters',
      availability: '9 AM - 9 PM',
      responseTime: 'Immediate',
      action: 'Call Now'
    }
  ];

  const faqItems = [
    {
      question: 'How long does it take to complete an order?',
      answer: 'Most orders start within 1-6 hours and complete within 24-72 hours depending on the service and quantity.'
    },
    {
      question: 'Are your services safe for my account?',
      answer: 'Yes, all our services are 100% safe and comply with platform guidelines. We use gradual delivery methods to ensure account safety.'
    },
    {
      question: 'What payment methods do you accept?',
      answer: 'We accept PayPal, Vodafone Cash, InstaPay, and direct bank transfers for your convenience.'
    },
    {
      question: 'Do you offer refunds?',
      answer: 'Yes, we offer refunds for orders that cannot be completed. Refill guarantees are also provided for most services.'
    },
    {
      question: 'Can I cancel my order?',
      answer: 'Orders can be cancelled if they haven\'t started processing yet. Once processing begins, cancellation may not be possible.'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <Link href="/" className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-sm">T</span>
                </div>
                <span className="text-xl font-bold text-gray-900">Trendz</span>
              </Link>
            </div>
            
            <nav className="hidden md:flex space-x-8">
              <Link href="/dashboard" className="text-gray-600 hover:text-purple-600">Dashboard</Link>
              <Link href="/services" className="text-gray-600 hover:text-purple-600">Services</Link>
              <Link href="/orders" className="text-gray-600 hover:text-purple-600">Orders</Link>
              <Link href="/support" className="text-purple-600 font-medium">Support</Link>
            </nav>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Page Header */}
        <div className="text-center mb-12">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">How can we help you?</h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Our support team is here to help you 24/7. Choose the best way to reach us or browse our FAQ.
          </p>
        </div>

        {/* Support Channels */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {supportChannels.map((channel, index) => (
            <div key={index} className="bg-white rounded-lg shadow p-6 text-center hover:shadow-md transition-shadow">
              <div className="text-4xl mb-4">{channel.icon}</div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">{channel.title}</h3>
              <p className="text-sm text-gray-600 mb-4">{channel.description}</p>
              <div className="space-y-2 mb-4">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-500">Availability:</span>
                  <span className="font-medium">{channel.availability}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-500">Response:</span>
                  <span className="font-medium">{channel.responseTime}</span>
                </div>
              </div>
              <button className="w-full bg-purple-600 text-white py-2 px-4 rounded-md hover:bg-purple-700 transition-colors">
                {channel.action}
              </button>
            </div>
          ))}
        </div>

        {/* Tabs */}
        <div className="bg-white rounded-lg shadow">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6">
              <button
                onClick={() => setActiveTab('contact')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'contact'
                    ? 'border-purple-500 text-purple-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Contact Form
              </button>
              <button
                onClick={() => setActiveTab('faq')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'faq'
                    ? 'border-purple-500 text-purple-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                FAQ
              </button>
              <button
                onClick={() => setActiveTab('status')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'status'
                    ? 'border-purple-500 text-purple-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                System Status
              </button>
            </nav>
          </div>

          <div className="p-6">
            {activeTab === 'contact' && (
              <div className="max-w-2xl mx-auto">
                <h3 className="text-lg font-medium text-gray-900 mb-6">Send us a message</h3>
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Name</label>
                      <input
                        type="text"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-purple-500 focus:border-purple-500"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Email</label>
                      <input
                        type="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-purple-500 focus:border-purple-500"
                        required
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Subject</label>
                    <input
                      type="text"
                      name="subject"
                      value={formData.subject}
                      onChange={handleInputChange}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-purple-500 focus:border-purple-500"
                      required
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Priority</label>
                      <select
                        name="priority"
                        value={formData.priority}
                        onChange={handleInputChange}
                        className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-purple-500 focus:border-purple-500"
                      >
                        <option value="low">Low</option>
                        <option value="medium">Medium</option>
                        <option value="high">High</option>
                        <option value="urgent">Urgent</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Category</label>
                      <select
                        name="category"
                        value={formData.category}
                        onChange={handleInputChange}
                        className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-purple-500 focus:border-purple-500"
                      >
                        <option value="general">General Inquiry</option>
                        <option value="order">Order Issue</option>
                        <option value="payment">Payment Problem</option>
                        <option value="technical">Technical Support</option>
                        <option value="refund">Refund Request</option>
                      </select>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Message</label>
                    <textarea
                      name="message"
                      value={formData.message}
                      onChange={handleInputChange}
                      rows={6}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-purple-500 focus:border-purple-500"
                      placeholder="Please describe your issue in detail..."
                      required
                    />
                  </div>

                  <button
                    type="submit"
                    className="w-full bg-purple-600 text-white py-3 px-4 rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 font-medium"
                  >
                    Send Message
                  </button>
                </form>
              </div>
            )}

            {activeTab === 'faq' && (
              <div className="max-w-3xl mx-auto">
                <h3 className="text-lg font-medium text-gray-900 mb-6">Frequently Asked Questions</h3>
                <div className="space-y-4">
                  {faqItems.map((item, index) => (
                    <div key={index} className="border border-gray-200 rounded-lg">
                      <button className="w-full px-6 py-4 text-left focus:outline-none">
                        <div className="flex justify-between items-center">
                          <h4 className="text-sm font-medium text-gray-900">{item.question}</h4>
                          <svg className="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                          </svg>
                        </div>
                      </button>
                      <div className="px-6 pb-4">
                        <p className="text-sm text-gray-600">{item.answer}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {activeTab === 'status' && (
              <div className="max-w-3xl mx-auto">
                <h3 className="text-lg font-medium text-gray-900 mb-6">System Status</h3>
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-4 bg-green-50 border border-green-200 rounded-lg">
                    <div className="flex items-center">
                      <div className="w-3 h-3 bg-green-500 rounded-full mr-3"></div>
                      <span className="font-medium text-gray-900">All Systems Operational</span>
                    </div>
                    <span className="text-sm text-gray-600">Last updated: 2 minutes ago</span>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {[
                      { name: 'Website', status: 'operational' },
                      { name: 'Order Processing', status: 'operational' },
                      { name: 'Payment System', status: 'operational' },
                      { name: 'API Services', status: 'operational' },
                      { name: 'Email Notifications', status: 'operational' },
                      { name: 'Support Chat', status: 'operational' }
                    ].map((service, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <span className="text-sm font-medium text-gray-900">{service.name}</span>
                        <div className="flex items-center">
                          <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                          <span className="text-sm text-green-600">Operational</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
